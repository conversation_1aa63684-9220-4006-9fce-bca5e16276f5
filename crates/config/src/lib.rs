use clap::Parser;
use derive_builder::Builder;
use url::Url;
// ClusterConfig will be defined below, so this import is no longer needed.

// Cluster Configuration (moved from relay::cluster)
#[derive(Parse<PERSON>, Debug, Builder, Clone)]
#[builder(setter(into), build_fn(skip))]
pub struct ClusterConfig {
	#[clap(long, env = "CLUSTER_ROOT")]
	pub cluster_root: Option<String>,

	#[clap(long, env = "CLUSTER_NODE")]
	pub cluster_node: Option<String>,
}

impl Default for ClusterConfig {
	fn default() -> Self {
		Self {
			cluster_root: None,
			cluster_node: None,
		}
	}
}

// Orchestrator Specific Configuration
#[derive(Pa<PERSON><PERSON>, Debug, Builder, Clone)]
#[builder(setter(into), build_fn(skip))] // build_fn(skip) because Default is comprehensive
pub struct OrchestratorConfig {
	#[clap(long, env = "ORCHESTRATOR_ID")]
	pub orchestrator_id: String,
}

impl Default for OrchestratorConfig {
	fn default() -> Self {
		Self {
			orchestrator_id: "orchestrator_default_01".to_string(),
		}
	}
}

// Worker Specific Configuration
#[derive(Parser, Debug, Builder, Clone)]
#[builder(setter(into), build_fn(skip))]
pub struct WorkerConfig {
	#[clap(long, env = "WORKER_ID")]
	pub worker_id: String,

	#[clap(long, env = "MAX_CONCURRENT_WORKERS")]
	pub max_concurrent_workers: usize,

	#[clap(long, env = "STATUS_UPDATE_INTERVAL_SEC")]
	pub status_update_interval_sec: u64,
}

impl Default for WorkerConfig {
	fn default() -> Self {
		Self {
			worker_id: "worker_default_01".to_string(),
			max_concurrent_workers: 50,
			status_update_interval_sec: 10,
		}
	}
}

// Relay Specific Configuration
#[derive(Parser, Builder, Clone)]
#[builder(setter(into), build_fn(skip))]
pub struct RelayConfig {
	/// Listen on this address, both TCP and UDP.
	#[clap(long, env = "BIND_ADDRESS")]
	pub bind: String,

	/// The TLS configuration.
	#[clap(flatten)]
	pub tls: moq_native::tls::Args,

	/// Cluster configuration.
	#[clap(flatten)]
	pub cluster: ClusterConfig,
}

impl Default for RelayConfig {
	fn default() -> Self {
		Self {
			bind: "[::]:443".to_string(),
			tls: moq_native::tls::Args::default(), // Assuming Args implements Default
			cluster: ClusterConfig::default(),     // ClusterConfig derives Default
		}
	}
}


#[derive(Parser, Debug, Builder, Clone)]
pub struct LoadBalancerConfig {
    pub node_id: Option<String>,
    pub max_streams: usize,
    pub enable_load_balancing: bool,
}

impl Default for LoadBalancerConfig {
    fn default() -> Self {
        Self {
            node_id: None,
            max_streams: 100,
            enable_load_balancing: true,
        }
    }
}

#[derive(Parser, Debug, Builder, Clone)]
pub struct HubConfig {
    #[clap(long, env = "HUB_ENABLE")]
    pub enable_hub: bool,
    
    #[clap(long, env = "HUB_MAX_SOURCES")]
    pub max_sources: usize,
    
    #[clap(long, env = "HUB_MAX_DESTINATIONS")]
    pub max_destinations: usize,
    
    #[clap(long, env = "HUB_HIGH_WATER_MARK")]
    pub high_water_mark: usize,
    
    #[clap(long, env = "HUB_AUTO_CLEANUP")]
    pub auto_cleanup: bool,
    
    #[clap(long, env = "HUB_STATUS_PORT")]
    pub status_port: u16,
}

impl Default for HubConfig {
    fn default() -> Self {
        Self {
            enable_hub: true,
            max_sources: 10,
            max_destinations: 100,
            high_water_mark: 1000,
            auto_cleanup: true,
            status_port: 8080,
        }
    }
}

// Main Configuration Struct
#[derive(Parser, Builder)]
#[clap(author, version, about, long_about = None)]
#[builder(setter(into))]
pub struct Config {
	// --- Shared Configurations ---
	#[clap(long, env = "MOQ_RELAY_URL")]
	pub relay_url: Url,

	#[clap(long, env = "RAW_STREAM_PREFIX")]
	pub raw_stream_prefix: String,

	#[clap(long, env = "REMUXED_STREAM_PREFIX")]
	pub remuxed_stream_prefix: String,

	#[clap(long, env = "WORKER_STATUS_PREFIX")]
	pub worker_status_prefix: String,

	#[clap(long, env = "WORKER_TASK_PREFIX")]
	pub worker_task_prefix: String,

	#[clap(long, env = "METRICS_PORT")]
	pub metrics_port: u16,

	/// Log configuration (shared across services).
	#[clap(flatten)]
	pub log: moq_native::log::Args,

	// --- Service Specific Configurations ---
	#[clap(flatten)]
	pub orchestrator: OrchestratorConfig,

	#[clap(flatten)]
	pub worker: WorkerConfig,

	#[clap(flatten)]
	pub relay: RelayConfig,

	// --- Service Type ---
	#[command(subcommand)]
	pub service_type: ServiceType,

	#[clap(flatten)]
	pub load_balancer: LoadBalancerConfig,

	#[clap(flatten)]
	pub hub: HubConfig,
}

impl Default for Config {
	fn default() -> Self {
		Self {
			// Shared
			relay_url: Url::parse("https://localhost:4443")
				.expect("Failed to parse default relay_url"),
			raw_stream_prefix: "raw/live/".to_string(),
			remuxed_stream_prefix: "remuxed/rtmp_ready/".to_string(),
			worker_status_prefix: "workers/rtmp/status/".to_string(),
			worker_task_prefix: "workers/rtmp/tasks/".to_string(),
			metrics_port: 9090,
			log: moq_native::log::Args::default(), // Assuming Args implements Default

			// Service Specific
			orchestrator: OrchestratorConfig::default(),
			worker: WorkerConfig::default(),
			relay: RelayConfig::default(),

			// Service Type
			service_type: ServiceType::Orchestrator, // Default service type
			load_balancer: LoadBalancerConfig::default(),
			hub: HubConfig::default(),
		}
	}
}

impl Config {
	pub fn load() -> Self {
		dotenvy::dotenv().ok();
		Config::parse() // Clap will use Config::default() for missing values
	}
}

#[derive(Parser, Clone, Debug, PartialEq)]
pub enum ServiceType {
	Orchestrator,
	Worker,
	Relay,
	RPC,
}

// Example of using the builder (e.g., in tests)
// The builder will now use Config::default() for its base.
#[cfg(test)]
mod tests {
	use super::*;

	#[test]
	fn test_build_config_with_overrides() {
		let config = ConfigBuilder::default()
			.orchestrator(OrchestratorConfig { orchestrator_id: "test_orc_001".to_string() })
			.worker(WorkerConfig { worker_id: "test_worker_001".to_string(), max_concurrent_workers: 10, status_update_interval_sec: 5 })
			.metrics_port(9999u16)      // Override shared
			.service_type(ServiceType::Worker) // Override service type
			.build()
			.unwrap();

		assert_eq!(config.orchestrator.orchestrator_id, "test_orc_001");
		assert_eq!(config.worker.worker_id, "test_worker_001");
		assert_eq!(config.worker.max_concurrent_workers, 10);
		assert_eq!(config.metrics_port, 9999);
		assert_eq!(config.service_type, ServiceType::Worker);
		assert_eq!(config.raw_stream_prefix, "raw/live/"); // From Config::default() shared
		assert_eq!(config.relay.bind, "[::]:443"); // From RelayConfig::default() via Config::default()
	}

	#[test]
	fn test_config_default_values() {
		let default_config = Config::default();
		assert_eq!(default_config.orchestrator.orchestrator_id, "orchestrator_default_01");
		assert_eq!(default_config.worker.worker_id, "worker_default_01");
		assert_eq!(default_config.relay.bind, "[::]:443");
		assert_eq!(default_config.metrics_port, 9090);
		assert_eq!(default_config.service_type, ServiceType::Orchestrator);
	}
}
