[package]
name = "streamhub"
version = "0.1.0"
edition = "2024"

[dependencies]
tokio = { version = "1.44.2", features = ["rt", "macros", "net", "io-util"] }
serde = { version = "1.0", features = ["derive"] }
uuid = { version = "1.0", features = ["v4"] }

tracing = "0.1.41"
tracing-subscriber = { version = "0.3.19", features = ["env-filter"] }
thiserror = "2.0.12"

stream_client = { path = "../../crates/stream_client" }

