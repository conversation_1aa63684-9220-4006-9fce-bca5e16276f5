use uuid::Uuid;

/// Errors that can occur in the streaming hub
#[derive(Debug, thiserror::Error)]
pub enum StreamHubError {
    #[error("Channel send error: {0}")]
    ChannelSendError(String),
    #[error("Source not found: {0}")]
    SourceNotFound(Uuid),
    #[error("Destination not found: {0}")]
    DestinationNotFound(Uuid),
    #[error("Route not found: source {0} to destination {1}")]
    RouteNotFound(Uuid, Uuid),
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),
    #[error("Source already exists: {0}")]
    SourceAlreadyExists(Uuid),
    #[error("Destination already exists: {0}")]
    DestinationAlreadyExists(Uuid),
    #[error("Route already exists: source {0} to destination {1}")]
    RouteAlreadyExists(Uuid, Uuid),
    #[error("Source is already active: {0}")]
    SourceAlreadyActive(Uuid),
    #[error("Source is not active: {0}")]
    SourceNotActive(Uuid),
    #[error("Destination is already active: {0}")]
    DestinationAlreadyActive(Uuid),
    #[error("Destination is not active: {0}")]
    DestinationNotActive(Uuid),
}

pub type Result<T> = std::result::Result<T, StreamHubError>;