use crate::{
    HubStatus,
    errors::{Result, StreamHubError},
    types::StreamControl,
};
use stream_client::{FrameDataSender, PullClient, PushClient};
use tokio::sync::mpsc;
use uuid::Uuid;

/// Handle for controlling the stream hub from outside
#[derive(Debug, Clone)]
pub struct StreamHubHandle {
    control_tx: mpsc::UnboundedSender<StreamControl>,
    input_tx: FrameDataSender,
}

impl StreamHubHandle {
    pub fn new(
        control_tx: mpsc::UnboundedSender<StreamControl>,
        input_tx: FrameDataSender,
    ) -> Self {
        Self {
            control_tx,
            input_tx,
        }
    }

    /// Add a new source (PullClient) for receiving frames
    pub async fn add_source(
        &self,
        client: PullClient,
        name: Option<String>,
    ) -> Result<()> {
        self.control_tx
            .send(StreamControl::AddSource { client, name })
            .map_err(|_| {
                StreamHubError::ChannelSendError("Failed to send add source control".to_string())
            })?;
        Ok(())
    }

    /// Remove a source by ID
    pub async fn remove_source(&self, id: Uuid) -> Result<()> {
        self.control_tx
            .send(StreamControl::RemoveSource { id })
            .map_err(|_| {
                StreamHubError::ChannelSendError("Failed to send remove source control".to_string())
            })?;
        Ok(())
    }

    /// Add a new destination (PushClient) for sending frames
    pub async fn add_destination(
        &self,
        client: PushClient,
        name: Option<String>,
    ) -> Result<()> {
        self.control_tx
            .send(StreamControl::AddDestination { client, name })
            .map_err(|_| {
                StreamHubError::ChannelSendError(
                    "Failed to send add destination control".to_string(),
                )
            })?;
        Ok(())
    }

    /// Remove a destination by ID
    pub async fn remove_destination(&self, id: Uuid) -> Result<()> {
        self.control_tx
            .send(StreamControl::RemoveDestination { id })
            .map_err(|_| {
                StreamHubError::ChannelSendError(
                    "Failed to send remove destination control".to_string(),
                )
            })?;
        Ok(())
    }

    /// Create a route from source to destination
    pub async fn add_route(&self, source_id: Uuid, destination_id: Uuid) -> Result<()> {
        self.control_tx
            .send(StreamControl::AddRoute {
                source_id,
                destination_id,
            })
            .map_err(|_| {
                StreamHubError::ChannelSendError("Failed to send add route control".to_string())
            })?;
        Ok(())
    }

    /// Remove a route from source to destination
    pub async fn remove_route(&self, source_id: Uuid, destination_id: Uuid) -> Result<()> {
        self.control_tx
            .send(StreamControl::RemoveRoute {
                source_id,
                destination_id,
            })
            .map_err(|_| {
                StreamHubError::ChannelSendError("Failed to send remove route control".to_string())
            })?;
        Ok(())
    }

    /// Start a source (begin pulling frames)
    pub async fn start_source(&self, id: Uuid) -> Result<()> {
        self.control_tx
            .send(StreamControl::StartSource { id })
            .map_err(|_| {
                StreamHubError::ChannelSendError("Failed to send start source control".to_string())
            })?;
        Ok(())
    }

    /// Stop a source
    pub async fn stop_source(&self, id: Uuid) -> Result<()> {
        self.control_tx
            .send(StreamControl::StopSource { id })
            .map_err(|_| {
                StreamHubError::ChannelSendError("Failed to send stop source control".to_string())
            })?;
        Ok(())
    }

    /// Start a destination (begin pushing frames)
    pub async fn start_destination(&self, id: Uuid) -> Result<()> {
        self.control_tx
            .send(StreamControl::StartDestination { id })
            .map_err(|_| {
                StreamHubError::ChannelSendError(
                    "Failed to send start destination control".to_string(),
                )
            })?;
        Ok(())
    }

    /// Stop a destination
    pub async fn stop_destination(&self, id: Uuid) -> Result<()> {
        self.control_tx
            .send(StreamControl::StopDestination { id })
            .map_err(|_| {
                StreamHubError::ChannelSendError(
                    "Failed to send stop destination control".to_string(),
                )
            })?;
        Ok(())
    }

    /// Shutdown the hub gracefully
    pub async fn add_destination_with_sender(
        &self,
        id: Uuid,
        sender: FrameDataSender,
        name: Option<String>,
    ) -> Result<()> {
        self.control_tx
            .send(StreamControl::AddDestinationWithSender { id, sender, name })
            .map_err(|_| {
                StreamHubError::ChannelSendError(
                    "Failed to send add destination with sender control".to_string(),
                )
            })?;
        Ok(())
    }

    pub async fn shutdown(&self) -> Result<()> {
        self.control_tx.send(StreamControl::Shutdown).map_err(|_| {
            StreamHubError::ChannelSendError("Failed to send shutdown control".to_string())
        })?;
        Ok(())
    }

    /// Get a clone of this handle for sharing across threads/tasks
    pub fn clone_handle(&self) -> Self {
        self.clone()
    }

    /// Get a clone of the input sender for sending frames
    pub fn input_tx(&self) -> FrameDataSender {
        self.input_tx.clone()
    }

    /// Check if the control channel is still open
    pub fn is_connected(&self) -> bool {
        !self.control_tx.is_closed()
    }

    /// Get hub status by sending a status query
    /// Note: This would require implementing a status query in StreamControl enum
    pub async fn get_hub_status(&self) -> Result<HubStatus> {
        let (response_tx, response_rx) = tokio::sync::oneshot::channel();

        self.control_tx
            .send(StreamControl::GetStatus { response_tx })
            .map_err(|_| {
                StreamHubError::ChannelSendError("Failed to send status request".to_string())
            })?;

        response_rx.await.map_err(|_| {
            StreamHubError::ChannelSendError("Failed to receive status response".to_string())
        })
    }
}

#[cfg(test)]
mod tests {
    use crate::core::*;

    #[tokio::test]
    async fn test_handle_creation() {
        let _hub = StreamHub::new();
        // Note: Core hub tests are now in core.rs
        // Handle tests should test the StreamHubHandle separately
    }

    #[tokio::test]
    async fn test_handle_cloning() {
        let _hub = StreamHub::new();
        // Note: Core hub tests are now in core.rs
        // Handle tests should test the StreamHubHandle separately
    }
}
