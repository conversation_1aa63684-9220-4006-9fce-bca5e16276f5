use crate::{StreamControl, StreamHub<PERSON><PERSON>le, errors::Result};
use std::{collections::HashMap, hash::RandomState};
use stream_client::{FrameData, FrameDataReceiver};
use tokio::sync::mpsc;
use uuid::Uuid;

use super::{Destination, StreamManager};

/// The main StreamHub struct that runs the core distribution logic
/// This follows the architecture from crates/streamer/src/hub_v2.rs
/// It receives frames from an input channel and distributes them to multiple destinations
pub struct StreamHub {
    manager: StreamManager,
    handle: StreamHubHandle,
    input_rx: FrameDataReceiver,
    control_rx: mpsc::UnboundedReceiver<StreamControl>,
}

impl StreamHub {
    /// High water mark for input queue - frames will be dropped if exceeded
    const HIGH_WATER_MARK: usize = 1000;

    /// Create a new StreamHub with input and control receivers
    pub fn new() -> Self {
        let (control_tx, control_rx) = mpsc::unbounded_channel::<StreamControl>();
        let (input_tx, input_rx) = mpsc::unbounded_channel::<FrameData>();

        Self {
            input_rx,
            control_rx,
            manager: StreamManager::new(input_tx.clone()),
            handle: StreamHubHandle::new(control_tx, input_tx),
        }
    }

    /// Runs the main distribution loop.
    /// This method should be spawned as a task.
    /// This is the core of the StreamHub - it distributes frames to all destinations
    pub async fn run(&mut self) -> Result<()> {
        tracing::info!("StreamHub started - Core Frame Distribution");

        let mut shutdown_requested = false;
        let mut input_queue_size = 0usize;

        loop {
            tokio::select! {
                 // Handle incoming frames - this is the core distribution logic
                 Some(frame_data) = self.input_rx.recv(), if !shutdown_requested => {
                    input_queue_size = input_queue_size.saturating_sub(1);

                    // Basic backpressure - drop frames if queue is too large
                    if input_queue_size > Self::HIGH_WATER_MARK {
                        tracing::warn!("Input queue size ({}) exceeds HIGH_WATER_MARK ({}), dropping frame",
                            input_queue_size, Self::HIGH_WATER_MARK);
                        continue;
                    }

                    tracing::trace!("Processing frame of {} bytes to {} destinations",
                        frame_data.len(),  self.manager.destinations.len());

                    self.distribute_frame_to_all_destinations(frame_data).await;
                }

                // Handle control messages
                Some(control_msg) = self.control_rx.recv() => {
                    match control_msg {
                        StreamControl::AddSource { client, name } => {
                            self.manager.add_source(client, name).await?;
                        }
                        StreamControl::RemoveSource { id } => {
                            self.manager.remove_source(id).await?;
                        }
                        StreamControl::AddDestination { client, name } => {
                            self.manager.add_destination(client, name).await?;
                        }
                        StreamControl::RemoveDestination { id } => {
                            self.manager.remove_destination(id).await?;
                        }
                        StreamControl::AddRoute { source_id, destination_id } => {
                            self.manager.add_route(source_id, destination_id).await?;
                        }
                        StreamControl::RemoveRoute { source_id, destination_id } => {
                            self.manager.remove_route(source_id, destination_id).await?;
                        }
                        StreamControl::StartSource { id } => {
                            self.manager.start_source(id).await?;
                        }
                        StreamControl::StopSource { id } => {
                            self.manager.stop_source(id).await?;
                        }
                        StreamControl::StartDestination { id } => {
                            self.manager.start_destination(id).await?;
                        }
                        StreamControl::StopDestination { id } => {
                            self.manager.stop_destination(id).await?;
                        }
                        StreamControl::AddDestinationWithSender { id: _, sender: _, name: _ } => {
                            // This control message is meant for the core StreamHub, not the manager
                            tracing::debug!("Received AddDestinationWithSender control - should be handled by core hub");
                        }
                        StreamControl::GetStatus { response_tx } => {
                            let status = self.manager.get_status();
                            if let Err(_) = response_tx.send(status) {
                                tracing::warn!("Failed to send status response");
                            }
                        }
                        StreamControl::Shutdown => {
                            tracing::info!("Shutdown signal received");
                            shutdown_requested = true;
                            self.input_rx.close();
                        }
                    }
                }

                else => {
                    if !shutdown_requested {
                        tracing::info!("All input/control channels closed, shutting down hub");
                    } else {
                        tracing::info!("StreamHub shutdown complete");
                    }
                    break;
                }
            }

            // Update input queue size tracking (simplified approach)
            input_queue_size = input_queue_size.saturating_add(1);
        }

        // Graceful shutdown
        self.manager.shutdown_all().await?;
        tracing::info!("StreamHub stopped");
        Ok(())
    }

    /// Core distribution logic - distribute frame to all destinations
    async fn distribute_frame_to_all_destinations(&mut self, frame_data: FrameData) {
        let mut to_remove = Vec::new();
        let source_id = frame_data.source_id();
        let dest_ids = self.get_destinations_by_source(source_id);

        // Distribute frame to all destinations
        for id in dest_ids {
            let frame_clone = frame_data.clone();
            if let Some(destination) = self.manager.destinations.get_mut(&id) {
                match destination.client.send_frame(frame_clone) {
                    Ok(()) => {
                        tracing::trace!("Sent frame to destination {}", id);
                    }
                    Err(_) => {
                        tracing::info!("Destination {} channel closed, removing", id);
                        to_remove.push(id);
                    }
                }
            }
        }

        // Remove closed destinations
        for id in to_remove {
            if let Some(dest) = self.manager.destinations.remove(&id) {
                tracing::info!(
                    "Removed destination {} ({})",
                    id,
                    dest.name.as_deref().unwrap_or("unnamed")
                );
            }
        }
    }

    /// Get the number of active destinations
    pub fn destination_count(&self) -> usize {
        self.manager.destinations.len()
    }

    /// Get a list of destination IDs and names
    pub fn get_destinations(&self) -> Vec<(Uuid, Option<String>)> {
        self.manager
            .destinations
            .values()
            .map(|dest| (dest.id, dest.name.clone()))
            .collect()
    }

    fn get_destinations_by_source(&self, source_id: Uuid) -> Vec<Uuid> {
        match self.manager.frame_distributors.get(&source_id) {
            Some(distributors) => distributors.clone(),
            None => Vec::new(),
        }
    }

    /// Check if a destination exists
    pub fn has_destination(&self, id: &Uuid) -> bool {
        self.manager.destinations.contains_key(id)
    }

    /// Get destination info by ID
    pub fn get_destination_info(&self, id: &Uuid) -> Option<(Uuid, Option<String>)> {
        self.manager
            .destinations
            .get(id)
            .map(|dest| (dest.id, dest.name.clone()))
    }

    /// Get reference to destinations for advanced operations
    pub fn destinations(&self) -> &HashMap<Uuid, Destination, RandomState> {
        &self.manager.destinations
    }

    /// Get mutable reference to destinations for advanced operations
    pub fn destinations_mut(&mut self) -> &mut HashMap<Uuid, Destination, RandomState> {
        &mut self.manager.destinations
    }

    pub fn handle(&self) -> StreamHubHandle {
        self.handle.clone_handle()
    }
}
