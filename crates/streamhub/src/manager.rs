use crate::{
    errors::{Result, StreamHubError},
    types::*,
};
use std::collections::HashMap;
use stream_client::{FrameDataSender, PullClient, PushClient};
use uuid::Uuid;

/// The StreamManager handles the lifecycle of sources, destinations, and routes
/// This is the management layer that sits above the core StreamHub distribution logic
pub struct StreamManager {
    pub(crate) input_sender: FrameDataSender,
    pub(crate) sources: HashMap<Uuid, Source>,
    pub(crate) destinations: HashMap<Uuid, Destination>,
    pub(crate) routes: HashMap<Route, FrameDataSender>,
    pub(crate) frame_distributors: HashMap<Uuid, Vec<Uuid>>, // source_id -> list of destination senders
}

impl StreamManager {
    /// Create a new StreamManager
    pub fn new(sender: FrameDataSender) -> Self {
        Self {
            input_sender: sender,
            sources: HashMap::new(),
            destinations: HashMap::new(),
            routes: HashMap::new(),
            frame_distributors: HashMap::new(),
        }
    }

    pub(crate) async fn add_source(
        &mut self,
        mut client: PullClient,
        name: Option<String>,
    ) -> Result<()> {
        let id = client.id();
        tracing::info!(
            "Adding source {} ({})",
            id,
            name.as_deref().unwrap_or("unnamed")
        );

        if self.sources.contains_key(&id) {
            return Err(StreamHubError::SourceAlreadyExists(id));
        }

        client.set_sender(self.input_sender.clone());
        let source = Source {
            id,
            client,
            name: name.clone(),
            is_active: false,
        };

        self.sources.insert(id, source);

        // Initialize distributor list for this source
        self.frame_distributors.insert(id, Vec::new());

        tracing::info!(
            "Source {} added successfully. Total sources: {}",
            id,
            self.sources.len()
        );
        Ok(())
    }

    pub(crate) async fn remove_source(&mut self, id: Uuid) -> Result<()> {
        tracing::info!("Removing source {}", id);

        if let Some(mut source) = self.sources.remove(&id) {
            // Stop the source if it's active
            if source.is_active {
                // TODO: Implement source stopping logic
                source.is_active = false;
            }

            // Remove all routes involving this source
            let routes_to_remove: Vec<Route> = self
                .routes
                .keys()
                .filter(|route| route.source_id == id)
                .copied()
                .collect();

            for route in routes_to_remove {
                self.routes.remove(&route);
                tracing::debug!(
                    "Removed route from source {} to destination {}",
                    route.source_id,
                    route.destination_id
                );
            }

            // Remove frame distributor
            self.frame_distributors.remove(&id);

            tracing::info!(
                "Source {} removed successfully. Total sources: {}",
                id,
                self.sources.len()
            );
        } else {
            return Err(StreamHubError::SourceNotFound(id));
        }

        Ok(())
    }

    pub(crate) async fn add_destination(
        &mut self,
        client: PushClient,
        name: Option<String>,
    ) -> Result<()> {
        let id = client.id();
        tracing::info!(
            "Adding destination {} ({})",
            id,
            name.as_deref().unwrap_or("unnamed")
        );

        if self.destinations.contains_key(&id) {
            return Err(StreamHubError::DestinationAlreadyExists(id));
        }

        let destination = Destination {
            id,
            client,
            name: name.clone(),
            is_active: false,
        };

        self.destinations.insert(id, destination);

        tracing::info!(
            "Destination {} added successfully. Total destinations: {}",
            id,
            self.destinations.len()
        );
        Ok(())
    }

    pub(crate) async fn remove_destination(&mut self, id: Uuid) -> Result<()> {
        tracing::info!("Removing destination {}", id);

        if let Some(mut destination) = self.destinations.remove(&id) {
            // Stop the destination if it's active
            if destination.is_active {
                // TODO: Implement destination stopping logic
                destination.is_active = false;
            }

            // Remove all routes involving this destination
            let routes_to_remove: Vec<Route> = self
                .routes
                .keys()
                .filter(|route| route.destination_id == id)
                .copied()
                .collect();

            for route in routes_to_remove {
                self.routes.remove(&route);
                tracing::debug!(
                    "Removed route from source {} to destination {}",
                    route.source_id,
                    route.destination_id
                );
            }

            tracing::info!(
                "Destination {} removed successfully. Total destinations: {}",
                id,
                self.destinations.len()
            );
        } else {
            return Err(StreamHubError::DestinationNotFound(id));
        }

        Ok(())
    }

    pub(crate) async fn add_route(&mut self, source_id: Uuid, destination_id: Uuid) -> Result<()> {
        tracing::info!(
            "Adding route from source {} to destination {}",
            source_id,
            destination_id
        );

        // Verify source and destination exist
        if !self.sources.contains_key(&source_id) {
            return Err(StreamHubError::SourceNotFound(source_id));
        }
        if !self.destinations.contains_key(&destination_id) {
            return Err(StreamHubError::DestinationNotFound(destination_id));
        }

        let route = Route {
            source_id,
            destination_id,
        };

        // Check if route already exists
        if self.routes.contains_key(&route) {
            return Err(StreamHubError::RouteAlreadyExists(
                source_id,
                destination_id,
            ));
        }

        // Create a channel for this route
        self.routes.insert(route, self.input_sender.clone());

        // Add sender to the source's distributor list
        if let Some(distributors) = self.frame_distributors.get_mut(&source_id) {
            distributors.push(destination_id);
        }

        tracing::info!(
            "Route added successfully from {} to {}. Total routes: {}",
            source_id,
            destination_id,
            self.routes.len()
        );
        Ok(())
    }

    pub(crate) async fn remove_route(
        &mut self,
        source_id: Uuid,
        destination_id: Uuid,
    ) -> Result<()> {
        tracing::info!(
            "Removing route from source {} to destination {}",
            source_id,
            destination_id
        );

        let route = Route {
            source_id,
            destination_id,
        };

        if self.routes.remove(&route).is_some() {
            // Remove sender from the source's distributor list
            if let Some(distributors) = self.frame_distributors.get_mut(&source_id) {
                // Note: This is a simplified removal - in a real implementation,
                // you'd need to track which sender corresponds to which destination
                distributors.retain(|_| true); // TODO: Implement proper sender removal
            }

            tracing::info!(
                "Route removed successfully from {} to {}. Total routes: {}",
                source_id,
                destination_id,
                self.routes.len()
            );
        } else {
            return Err(StreamHubError::RouteNotFound(source_id, destination_id));
        }

        Ok(())
    }

    pub(crate) async fn start_source(&mut self, id: Uuid) -> Result<()> {
        if let Some(source) = self.sources.get_mut(&id) {
            if source.is_active {
                return Err(StreamHubError::SourceAlreadyActive(id));
            }

            tracing::info!(
                "Starting source {} ({})",
                id,
                source.name.as_deref().unwrap_or("unnamed")
            );

            // TODO: Start the PullClient to begin receiving frames
            // This would typically involve starting a background task that receives frames
            // and sends them through the source.sender channel

            source.is_active = true;
            tracing::info!("Source {} started successfully", id);
        } else {
            return Err(StreamHubError::SourceNotFound(id));
        }
        Ok(())
    }

    pub(crate) async fn stop_source(&mut self, id: Uuid) -> Result<()> {
        if let Some(source) = self.sources.get_mut(&id) {
            if !source.is_active {
                return Err(StreamHubError::SourceNotActive(id));
            }

            tracing::info!(
                "Stopping source {} ({})",
                id,
                source.name.as_deref().unwrap_or("unnamed")
            );

            // TODO: Stop the PullClient
            source.is_active = false;
            tracing::info!("Source {} stopped successfully", id);
        } else {
            return Err(StreamHubError::SourceNotFound(id));
        }
        Ok(())
    }

    pub(crate) async fn start_destination(&mut self, id: Uuid) -> Result<()> {
        if let Some(destination) = self.destinations.get_mut(&id) {
            if destination.is_active {
                return Err(StreamHubError::DestinationAlreadyActive(id));
            }

            tracing::info!(
                "Starting destination {} ({})",
                id,
                destination.name.as_deref().unwrap_or("unnamed")
            );

            // TODO: Start the PushClient to begin sending frames
            // This would typically involve starting a background task that receives frames
            // from the destination.receiver channel and pushes them via the PushClient

            destination.is_active = true;
            tracing::info!("Destination {} started successfully", id);
        } else {
            return Err(StreamHubError::DestinationNotFound(id));
        }
        Ok(())
    }

    pub(crate) async fn stop_destination(&mut self, id: Uuid) -> Result<()> {
        if let Some(destination) = self.destinations.get_mut(&id) {
            if !destination.is_active {
                return Err(StreamHubError::DestinationNotActive(id));
            }

            tracing::info!(
                "Stopping destination {} ({})",
                id,
                destination.name.as_deref().unwrap_or("unnamed")
            );

            // TODO: Stop the PushClient
            destination.is_active = false;
            tracing::info!("Destination {} stopped successfully", id);
        } else {
            return Err(StreamHubError::DestinationNotFound(id));
        }
        Ok(())
    }

    pub(crate) async fn shutdown_all(&mut self) -> Result<()> {
        tracing::info!("Shutting down all sources and destinations...");

        // Stop all active sources
        let source_ids: Vec<Uuid> = self.sources.keys().copied().collect();
        for id in source_ids {
            if self.sources.get(&id).map(|s| s.is_active).unwrap_or(false) {
                if let Err(e) = self.stop_source(id).await {
                    tracing::error!("Error stopping source {}: {}", id, e);
                }
            }
        }

        // Stop all active destinations
        let destination_ids: Vec<Uuid> = self.destinations.keys().copied().collect();
        for id in destination_ids {
            if self
                .destinations
                .get(&id)
                .map(|d| d.is_active)
                .unwrap_or(false)
            {
                if let Err(e) = self.stop_destination(id).await {
                    tracing::error!("Error stopping destination {}: {}", id, e);
                }
            }
        }

        tracing::info!("All sources and destinations shut down");
        Ok(())
    }

    /// Get hub status information
    pub fn get_status(&self) -> HubStatus {
        HubStatus {
            source_count: self.sources.len(),
            destination_count: self.destinations.len(),
            route_count: self.routes.len(),
            active_sources: self.sources.values().filter(|s| s.is_active).count(),
            active_destinations: self.destinations.values().filter(|d| d.is_active).count(),
        }
    }

    /// Get list of sources
    pub fn get_sources(&self) -> Vec<SourceInfo> {
        self.sources
            .values()
            .map(|source| SourceInfo {
                id: source.id,
                name: source.name.clone(),
                is_active: source.is_active,
            })
            .collect()
    }

    /// Get list of destinations
    pub fn get_destinations(&self) -> Vec<DestinationInfo> {
        self.destinations
            .values()
            .map(|dest| DestinationInfo {
                id: dest.id,
                name: dest.name.clone(),
                is_active: dest.is_active,
            })
            .collect()
    }

    /// Get list of routes
    pub fn get_routes(&self) -> Vec<RouteInfo> {
        self.routes
            .keys()
            .map(|route| RouteInfo {
                source_id: route.source_id,
                destination_id: route.destination_id,
            })
            .collect()
    }

    pub fn get_destinations_by_source(&self, source_id: Uuid) -> Vec<DestinationInfo> {
        self.routes
            .keys()
            .filter(|route| route.source_id == source_id)
            .map(|route| {
                self.destinations
                    .get(&route.destination_id)
                    .map(|dest| DestinationInfo {
                        id: dest.id,
                        name: dest.name.clone(),
                        is_active: dest.is_active,
                    })
                    .unwrap_or_else(|| DestinationInfo {
                        id: route.destination_id,
                        name: None,
                        is_active: false,
                    })
            })
            .collect()
    }
}
