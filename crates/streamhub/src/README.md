# Stream Hub Architecture

The Stream Hub is a sophisticated many-to-many stream distribution system that enables flexible routing of media streams from multiple sources to multiple destinations. This document explains the architecture and how all modules work together.

## Overview

The hub architecture is designed with separation of concerns, splitting functionality into distinct layers:

1. **Core Distribution Layer** (`core.rs`) - Handles actual frame distribution
2. **Management Layer** (`manager.rs`) - Manages source/destination lifecycle and routing
3. **Control Interface** (`handle.rs`) - Provides external API for hub control
4. **Type Definitions** (`types.rs`) - Shared data structures
5. **Error Handling** (`errors.rs`) - Comprehensive error management

## Architecture Diagram

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   PullClient    │    │   PullClient    │    │   PullClient    │
│   (Source 1)    │    │   (Source 2)    │    │   (Source N)    │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          ▼                      ▼                      ▼
┌─────────────────────────────────────────────────────────────────┐
│                    StreamManager                                │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐              │
│  │   Source    │  │   Source    │  │   Source    │              │
│  │   Route     │  │   Route     │  │   Route     │              │
│  │  Management │  │  Management │  │  Management │              │
│  └─────────────┘  └─────────────┘  └─────────────┘              │
└─────────────────────┬───────────────────────────────────────────┘
                      │ Frame Data
                      ▼
┌─────────────────────────────────────────────────────────────────┐
│                     StreamHub (Core)                            │
│                 Frame Distribution Engine                       │
│  ┌───────────────────────────────────────────────────────────┐  │
│  │  Input Queue → Distribution Logic → Multiple Outputs      │  │
│  └───────────────────────────────────────────────────────────┘  │
└─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────┘
          │         │         │         │         │         │
          ▼         ▼         ▼         ▼         ▼         ▼
┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐
│ PushClient  │ │ PushClient  │ │ PushClient  │ │ PushClient  │
│(Destination)│ │(Destination)│ │(Destination)│ │(Destination)│
└─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘
```

## Module Breakdown

### 1. Core Distribution (`core.rs`)

**Purpose**: High-performance frame distribution engine following the `hub_v2.rs` pattern.

**Key Components**:
- `StreamHub`: Main distribution struct
- `CoreStreamControl`: Simplified control messages for distribution
- `StreamDestination`: Destination information for frame delivery

**Responsibilities**:
- Receives frames from single input channel
- Distributes frames to all connected destinations
- Handles backpressure (HIGH_WATER_MARK = 1000 frames)
- Automatic cleanup of closed destination channels
- Core distribution logic: `distribute_frame_to_all_destinations()`

**Architecture Pattern**:
```rust
Input Channel → StreamHub → Multiple Destination Channels
```

**Key Features**:
- Non-blocking frame distribution using `try_send()`
- Automatic removal of failed destinations
- Input queue monitoring and frame dropping for backpressure
- Tokio-based async architecture

### 2. Stream Management (`manager.rs`)

**Purpose**: Manages the lifecycle of sources, destinations, and routes in a many-to-many configuration.

**Key Components**:
- `StreamManager`: Main management engine
- Source management (PullClient lifecycle)
- Destination management (PushClient lifecycle)
- Route management (source → destination mappings)

**Responsibilities**:
- Add/Remove sources (PullClients)
- Add/Remove destinations (PushClients)
- Create/Remove routes between sources and destinations
- Start/Stop individual components
- Status monitoring and reporting
- Graceful shutdown coordination

**Management Operations**:
```rust
// Source Management
add_source(id, pull_client, name) → Result<()>
remove_source(id) → Result<()>
start_source(id) → Result<()>
stop_source(id) → Result<()>

// Destination Management  
add_destination(id, push_client, name) → Result<()>
remove_destination(id) → Result<()>
start_destination(id) → Result<()>
stop_destination(id) → Result<()>

// Route Management
add_route(source_id, destination_id) → Result<()>
remove_route(source_id, destination_id) → Result<()>

// Status & Monitoring
get_status() → HubStatus
get_sources() → Vec<SourceInfo>
get_destinations() → Vec<DestinationInfo>
get_routes() → Vec<RouteInfo>
```

### 3. Control Interface (`handle.rs`)

**Purpose**: Provides external API for controlling the hub system.

**Key Components**:
- `StreamHubHandle`: Main control interface
- Async methods for all management operations
- Channel-based communication with internal components

**API Surface**:
```rust
pub struct StreamHubHandle {
    control_tx: mpsc::UnboundedSender<StreamControl>
}

impl StreamHubHandle {
    // Source Operations
    pub async fn add_source(&self, id: Uuid, client: PullClient, name: Option<String>) -> Result<()>
    pub async fn remove_source(&self, id: Uuid) -> Result<()>
    pub async fn start_source(&self, id: Uuid) -> Result<()>
    pub async fn stop_source(&self, id: Uuid) -> Result<()>
    
    // Destination Operations
    pub async fn add_destination(&self, id: Uuid, client: PushClient, name: Option<String>) -> Result<()>
    pub async fn remove_destination(&self, id: Uuid) -> Result<()>
    pub async fn start_destination(&self, id: Uuid) -> Result<()>
    pub async fn stop_destination(&self, id: Uuid) -> Result<()>
    
    // Route Operations
    pub async fn add_route(&self, source_id: Uuid, destination_id: Uuid) -> Result<()>
    pub async fn remove_route(&self, source_id: Uuid, destination_id: Uuid) -> Result<()>
    
    // System Operations
    pub async fn shutdown(&self) -> Result<()>
    pub fn clone_handle(&self) -> Self
    pub fn is_connected(&self) -> bool
}
```

### 4. Type Definitions (`types.rs`)

**Purpose**: Centralized type definitions shared across all hub modules.

**Key Types**:

```rust
// Control Messages
pub enum StreamControl {
    AddSource { id: Uuid, client: PullClient, name: Option<String> },
    RemoveSource { id: Uuid },
    AddDestination { id: Uuid, client: PushClient, name: Option<String> },
    AddDestinationWithSender { id: Uuid, sender: FrameDataSender, name: Option<String> },
    RemoveDestination { id: Uuid },
    AddRoute { source_id: Uuid, destination_id: Uuid },
    RemoveRoute { source_id: Uuid, destination_id: Uuid },
    StartSource { id: Uuid },
    StopSource { id: Uuid },
    StartDestination { id: Uuid },
    StopDestination { id: Uuid },
    Shutdown,
}

// Source Definition
pub struct Source {
    pub id: Uuid,
    pub client: crate::moq_pull::PullClient,
    pub name: Option<String>,
    pub sender: FrameDataSender,
    pub is_active: bool,
}

// Destination Definition  
pub struct Destination {
    pub id: Uuid,
    pub client: rtmp::PushClient,
    pub name: Option<String>,
    pub receiver: FrameDataReceiver,
    pub is_active: bool,
}

// Route Definition
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
pub struct Route {
    pub source_id: Uuid,
    pub destination_id: Uuid,
}

// Status and Info Types
pub struct HubStatus { /* ... */ }
pub struct SourceInfo { /* ... */ }
pub struct DestinationInfo { /* ... */ }
pub struct RouteInfo { /* ... */ }
```

### 5. Error Handling (`errors.rs`)

**Purpose**: Comprehensive error management for all hub operations.

**Error Types**:
```rust
pub enum StreamHubError {
    ChannelSendError(String),
    SourceNotFound(Uuid),
    DestinationNotFound(Uuid),
    RouteNotFound(Uuid, Uuid),
    SourceAlreadyExists(Uuid),
    DestinationAlreadyExists(Uuid),
    RouteAlreadyExists(Uuid, Uuid),
    SourceAlreadyActive(Uuid),
    SourceNotActive(Uuid),
    DestinationAlreadyActive(Uuid),
    DestinationNotActive(Uuid),
}

pub type Result<T> = std::result::Result<T, StreamHubError>;
```

## Communication Flow

### 1. Control Flow
```
External API → StreamHubHandle → StreamControl Messages → StreamManager
```

### 2. Data Flow  
```
PullClient → Source Channels → Route Channels → StreamHub → Destination Channels → PushClient
```

### 3. Management Flow
```
StreamManager ←→ StreamHub (for destination management)
StreamManager → Sources/Destinations (lifecycle management)
```

## Usage Examples

### Basic Setup
```rust
use edify::hub::{create_stream_hub, StreamHubHandle, StreamManager};

// Create core distribution hub
let (mut stream_hub, core_control_tx, input_tx) = create_stream_hub();

// Create management system  
let (mgmt_control_tx, mgmt_control_rx) = mpsc::unbounded_channel();
let mut stream_manager = StreamManager {
    control_rx: mgmt_control_rx,
    sources: HashMap::new(),
    destinations: HashMap::new(),
    routes: HashMap::new(),
    frame_distributors: HashMap::new(),
};

// Create external control handle
let handle = StreamHubHandle::new(mgmt_control_tx);

// Start background tasks
tokio::spawn(async move { stream_hub.run().await });
tokio::spawn(async move { stream_manager.run().await });
```

### Adding Sources and Destinations
```rust
// Add a source
let source_id = Uuid::new_v4();
let pull_client = PullClient::new(/* ... */);
handle.add_source(source_id, pull_client, Some("RTMP Input".to_string())).await?;

// Add a destination
let dest_id = Uuid::new_v4();  
let push_client = PushClient::new("rtmp://example.com/live/stream").await?;
handle.add_destination(dest_id, push_client, Some("YouTube".to_string())).await?;

// Create route
handle.add_route(source_id, dest_id).await?;

// Start components
handle.start_source(source_id).await?;
handle.start_destination(dest_id).await?;
```

### Advanced Usage - Multiple Routes
```rust
// One source to multiple destinations (1:N)
let source_id = Uuid::new_v4();
let youtube_dest = Uuid::new_v4();
let twitch_dest = Uuid::new_v4();
let facebook_dest = Uuid::new_v4();

// Add all components...
handle.add_route(source_id, youtube_dest).await?;
handle.add_route(source_id, twitch_dest).await?;
handle.add_route(source_id, facebook_dest).await?;

// Multiple sources to one destination (N:1)  
let mixer_dest = Uuid::new_v4();
handle.add_route(source1_id, mixer_dest).await?;
handle.add_route(source2_id, mixer_dest).await?;
handle.add_route(source3_id, mixer_dest).await?;
```

## Key Design Decisions

### 1. **Separation of Concerns**
- **Core**: Purely focused on high-performance frame distribution
- **Manager**: Handles complex source/destination lifecycle and routing
- **Handle**: Clean external API abstraction

### 2. **Channel-Based Architecture**  
- Uses tokio unbounded channels for async communication
- Enables non-blocking operations
- Facilitates clean shutdown and error handling

### 3. **Many-to-Many Support**
- Sources and destinations managed independently
- Flexible routing system allows any source → destination mapping
- Supports complex scenarios (1:N, N:1, N:M)

### 4. **Error Resilience**
- Automatic cleanup of failed channels
- Comprehensive error types for all operations
- Graceful degradation when components fail

### 5. **Performance Considerations**
- Input queue monitoring with configurable high water mark
- Non-blocking frame distribution using `try_send()`
- Efficient HashMap-based lookups for routes and destinations

## Testing

The hub includes comprehensive tests covering:

- **Unit Tests**: Individual component functionality
- **Integration Tests**: End-to-end frame distribution
- **Performance Tests**: Backpressure and load handling
- **Error Tests**: Failure scenarios and recovery

Run tests with:
```bash
cargo test --package edify --lib hub
```

## Future Enhancements

1. **Metrics and Monitoring**: Add detailed performance metrics
2. **Dynamic Routing**: Runtime route modification without stopping streams  
3. **Load Balancing**: Intelligent distribution for multiple destinations
4. **Stream Transformation**: Frame modification during routing
5. **Persistent Configuration**: Save/load hub configurations
6. **Web Dashboard**: Real-time monitoring and control interface

This architecture provides a solid foundation for complex stream distribution scenarios while maintaining clean separation of concerns and high performance.