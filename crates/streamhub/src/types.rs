use stream_client::{Frame<PERSON>ata<PERSON><PERSON>, PullClient, PushClient};
use uuid::Uuid;

/// Control messages for managing the stream hub
#[derive(Debug)]
pub enum StreamControl {
    /// Add a new source (PullClient)
    AddSource {
        client: PullClient,
        name: Option<String>,
    },
    /// Remove a source by ID
    RemoveSource { id: Uuid },
    /// Add a new destination (PushClient) - for manager
    AddDestination {
        client: PushClient,
        name: Option<String>,
    },
    /// Add a new destination with sender - for core hub
    AddDestinationWithSender {
        id: Uuid,
        sender: FrameDataSender,
        name: Option<String>,
    },
    /// Remove a destination by ID
    RemoveDestination { id: Uuid },
    /// Create a route from source to destination
    AddRoute {
        source_id: Uuid,
        destination_id: Uuid,
    },
    /// Remove a route from source to destination
    RemoveRoute {
        source_id: Uuid,
        destination_id: Uuid,
    },
    /// Start a source (begin pulling/receiving frames)
    StartSource { id: Uuid },
    /// Stop a source
    StopSource { id: Uuid },
    /// Start a destination (begin pushing frames)
    StartDestination { id: Uuid },
    /// Stop a destination
    StopDestination { id: Uuid },
    /// Shutdown the hub gracefully
    Shutdown,
    /// Get hub status (sources, destinations, routes count)
    GetStatus {
        response_tx: tokio::sync::oneshot::Sender<HubStatus>,
    },
}
/// Information about a source (input stream)
pub struct Source {
    pub id: Uuid,
    pub client: PullClient,
    pub name: Option<String>,
    pub is_active: bool,
}

/// Information about a destination (output stream)
#[derive(Debug)]
pub struct Destination {
    pub id: Uuid,
    pub client: PushClient,
    pub name: Option<String>,
    pub is_active: bool,
}

/// A route connecting a source to a destination
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
pub struct Route {
    pub source_id: Uuid,
    pub destination_id: Uuid,
}

/// Status information about the hub
#[derive(Debug, Clone)]
pub struct HubStatus {
    pub source_count: usize,
    pub destination_count: usize,
    pub route_count: usize,
    pub active_sources: usize,
    pub active_destinations: usize,
}

/// Information about a source
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct SourceInfo {
    pub id: Uuid,
    pub name: Option<String>,
    pub is_active: bool,
}

/// Information about a destination
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct DestinationInfo {
    pub id: Uuid,
    pub name: Option<String>,
    pub is_active: bool,
}

/// Information about a route
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct RouteInfo {
    pub source_id: Uuid,
    pub destination_id: Uuid,
}
