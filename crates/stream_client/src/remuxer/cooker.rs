use bytes::{BufMut, Bytes, BytesMut};
use bytesio::bytes_writer::BytesWriter;
use indexmap::IndexMap;

use super::{
    Marshal,
    flv_tag::{AudioTagHeader, VideoTagHeader},
};

use crate::rtmp::{
    types::{AvcPacketType, FrameType, SoundFormat},
    errors::RemuxerError,
};

use xflv::amf0::{Amf0ValueType, amf0_writer::Amf0Writer};

#[derive(Default)]
pub struct RtmpCooker {}

impl RtmpCooker {
    pub fn new() -> Self {
        Self {}
    }

    pub fn gen_meta_data(
        width: u32,
        height: u32,
        video_codec_id_num: u8, // e.g., 7 for H.264
        audio_codec_id_num: u8, // e.g., 10 for AAC
    ) -> Result<BytesMut, RemuxerError> {
        let mut amf_writer = Amf0Writer::new();
        amf_writer.write_string(&String::from("@setDataFrame"))?;
        amf_writer.write_string(&String::from("onMetaData"))?;

        let mut properties = IndexMap::new();
        if width > 0 && height > 0 {
            properties.insert(String::from("width"), Amf0ValueType::Number(width as f64));
            properties.insert(String::from("height"), Amf0ValueType::Number(height as f64));
        }
        if video_codec_id_num > 0 {
            properties.insert(
                String::from("videocodecid"),
                Amf0ValueType::Number(video_codec_id_num as f64),
            );
        }
        if audio_codec_id_num > 0 {
            properties.insert(
                String::from("audiocodecid"),
                Amf0ValueType::Number(audio_codec_id_num as f64),
            );
        }
        // Add other metadata if available: framerate, bitrate, etc.
        amf_writer.write_eacm_array(&properties)?;

        Ok(amf_writer.extract_current_bytes())
    }

    pub fn gen_video_seq_header(
        avc_config_payload: &Bytes, // This is the AVCDecoderConfigurationRecord
        video_codec_flv: u8,        // e.g., define::AvcCodecId::H264 as u8
    ) -> Result<BytesMut, RemuxerError> {
        let video_tag_header = VideoTagHeader {
            frame_type: FrameType::KeyFrame as u8,
            codec_id: video_codec_flv,
            avc_packet_type: AvcPacketType::SequenceHeader as u8,
            composition_time: 0,
        };
        let tag_header_data = video_tag_header.marshal()?;

        let mut writer = BytesWriter::new();
        writer.write(&tag_header_data)?;
        writer.write(avc_config_payload)?;

        Ok(writer.extract_current_bytes())
    }

    pub fn gen_audio_seq_header(
        sample_rate: u32,
        channels: u8,
        // audio_codec_profile_object_type: u8, // e.g., 2 for AAC-LC
    ) -> Result<BytesMut, RemuxerError> {
        // Assuming AAC for now
        let object_type = 2; // AAC LC is common
        let sampling_freq_index = match sample_rate {
            96000 => 0,
            88200 => 1,
            64000 => 2,
            48000 => 3,
            44100 => 4,
            32000 => 5,
            24000 => 6,
            22050 => 7,
            16000 => 8,
            12000 => 9,
            11025 => 10,
            8000 => 11,
            7350 => 12,
            _ => {
                return Err(RemuxerError::InvalidParam(format!(
                    "Unsupported AAC sample rate: {}",
                    sample_rate
                )));
            }
        };
        let channel_config = channels;
        if !(1..=8).contains(&channel_config) {
            return Err(RemuxerError::InvalidParam(format!(
                "Unsupported AAC channel count: {}",
                channels
            )));
        }

        let mut asc = BytesMut::with_capacity(2);
        asc.put_u8((object_type << 3) | (sampling_freq_index >> 1) as u8);
        asc.put_u8(((sampling_freq_index & 0x01) << 7) as u8 | (channel_config << 3));

        let audio_tag_header = AudioTagHeader {
            sound_format: SoundFormat::AAC as u8,
            sound_rate: 3, // FLV: For AAC, always 3 (44kHz)
            sound_size: 1, // FLV: For AAC, always 1 (16Bit)
            sound_type: if channels == 1 { 0 } else { 1 }, // FLV: 0=Mono, 1=Stereo
            aac_packet_type: 0, // AAC sequence header
        };
        let tag_header_data = audio_tag_header.marshal()?;

        let mut writer = BytesWriter::new();
        writer.write(&tag_header_data)?;
        writer.write(&asc)?;

        Ok(writer.extract_current_bytes())
    }

    pub fn gen_video_frame_data(
        video_payload: &Bytes, // Raw NALU data (e.g., length-prefixed for H.264)
        is_keyframe: bool,
        composition_time: i32,
        video_codec_flv: u8, // e.g., define::AvcCodecId::H264 as u8
    ) -> Result<BytesMut, RemuxerError> {
        let frame_type = if is_keyframe {
            FrameType::KeyFrame as u8
        } else {
            FrameType::InterFrame as u8
        };

        let video_tag_header = VideoTagHeader {
            frame_type,
            codec_id: video_codec_flv,
            avc_packet_type: AvcPacketType::Nalu as u8,
            composition_time,
        };
        let tag_header_data = video_tag_header.marshal()?;

        let mut writer = BytesWriter::new();
        writer.write(&tag_header_data)?;
        writer.write(video_payload)?;

        Ok(writer.extract_current_bytes())
    }

    pub fn gen_audio_frame_data(
        audio_payload: &Bytes,
        channels: u8,        // Needed for FLV header sound_type
        audio_codec_flv: u8, // e.g., define::SoundFormat::AAC as u8
    ) -> Result<BytesMut, RemuxerError> {
        // Assuming AAC
        let audio_tag_header = AudioTagHeader {
            sound_format: audio_codec_flv,
            sound_rate: 3, // FLV: For AAC, always 3 (44kHz)
            sound_size: 1, // FLV: For AAC, always 1 (16Bit)
            sound_type: if channels == 1 { 0 } else { 1 }, // FLV: 0=Mono, 1=Stereo
            aac_packet_type: 1, // AAC raw data
        };
        let tag_header_data = audio_tag_header.marshal()?;

        let mut writer = BytesWriter::new();
        writer.write(&tag_header_data)?;
        writer.write(audio_payload)?;

        Ok(writer.extract_current_bytes())
    }
}
