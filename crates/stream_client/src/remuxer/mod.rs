pub mod cooker;
pub mod flv_tag;
pub mod moq2rtmp;
pub mod handle;

use crate::rtmp::errors::RemuxerError;
use async_trait::async_trait;
use bytes::{Bytes, BytesMut};
use crate::rtmp::types::RemuxType;
use moq2rtmp::MoQ2RtmpRemuxer;

pub trait Unmarshal<T1, T2> {
    fn unmarshal(reader: T1) -> T2
    where
        Self: Sized;
}

pub trait Marshal<T> {
    fn marshal(&self) -> T;
}

pub struct RtmpRemuxer {}

impl RtmpRemuxer {
    pub fn create(kind: RemuxType) -> Box<dyn Remuxer> {
        match kind {
            RemuxType::MoqRemuxer => Box::new(MoQ2RtmpRemuxer::new()),
        }
    }
}

#[async_trait]
pub trait Remuxer: Send + Sync {
    /// Initializes the remuxer with track information.
    /// Should be called once per set of tracks.
    /// Returns a BytesMut containing audio sequence headers.
    /// The `sample_rate` and `channels` parameters are used to generate the audio sequence header.
    /// This is typically used for AAC or Opus audio streams.
    /// For AAC, the `sample_rate` is usually 44100 or 48000, and `channels` is 2 for stereo.
    /// For Opus, the `sample_rate` is typically 48000, and `channels` is usually 1 for mono.
    async fn gen_audio_seq_header(
        &mut self,
        sample_rate: u32,
        channels: u32,
    ) -> Result<BytesMut, RemuxerError>;

    /// Initializes the remuxer with video sequence header information.
    /// Should be called once per video track.
    /// Returns a BytesMut containing video sequence headers.
    /// The `desc` parameter contains the video codec description, which may include codec ID, profile, level, etc.
    /// This is typically used for H.264 or HEVC video streams.
    /// For H.264, the `desc` may contain the AVCDecoderConfigurationRecord.
    async fn gen_video_seq_header(&mut self, desc: &Bytes) -> Result<BytesMut, RemuxerError>;

    /// Processes a single video frame.
    /// Returns a remuxed packets with RTMP/FLV timestamps (in milliseconds).
    async fn remux_video_frame(
        &mut self,
        frame: (Bytes, u32, bool),
    ) -> Result<(BytesMut, u32), RemuxerError>;

    /// Processes a single audio frame.
    /// Returns a remuxed packets with RTMP/FLV timestamps (in milliseconds).
    async fn remux_audio_frame(
        &mut self,
        frame: (Bytes, u32, bool),
    ) -> Result<(BytesMut, u32), RemuxerError>;

    /// Signals the end of the stream, allowing the remuxer to flush any pending data.
    async fn finalize(&mut self) -> Result<Vec<(BytesMut, u32)>, RemuxerError> {
        Ok(Vec::new()) // Default no-op
    }
}
