use bytes::{Bytes, BytesMut};

use super::{
    Remuxer,
    cooker::Rtmp<PERSON>ooker,
};

use crate::rtmp::{
    types::{AvcCodecId, SoundFormat},
    errors::RemuxerError,
};

pub struct MoQ2RtmpRemuxer {
    // Video state
    video_timescale: u32,
    base_video_ts_us: u32,
    video_codec_flv: u8,

    // Audio state
    audio_timescale: u32,
    base_audio_ts_us: u32,
    audio_codec_flv: u8,
    audio_channels: u32,

    // Flags
    metadata_generated: bool,
    video_seq_header_generated: bool,
    audio_seq_header_generated: bool,
}

impl MoQ2RtmpRemuxer {
    pub fn new() -> Self {
        Self {
            video_timescale: 90000,
            base_video_ts_us: 0,
            video_codec_flv: 0,

            audio_timescale: 44100,
            base_audio_ts_us: 0,
            audio_codec_flv: 0,
            audio_channels: 0,

            metadata_generated: false,
            video_seq_header_generated: false,
            audio_seq_header_generated: false,
        }
    }
}

#[async_trait::async_trait]
impl Remuxer for MoQ2RtmpRemuxer {
    async fn gen_audio_seq_header(
        &mut self,
        sample_rate: u32,
        channels: u32,
    ) -> Result<BytesMut, RemuxerError> {
        self.audio_timescale = sample_rate;
        self.audio_channels = channels;
        self.audio_codec_flv = SoundFormat::AAC as u8; // AAC codec ID for FLV
        let audio_header =
            RtmpCooker::gen_audio_seq_header(self.audio_timescale, self.audio_channels as u8);
        match audio_header {
            Ok(header) => {
                self.audio_seq_header_generated = true;
                Ok(header)
            }
            Err(e) => Err(RemuxerError::Other(e.to_string())),
        }
    }

    async fn gen_video_seq_header(&mut self, desc: &Bytes) -> Result<BytesMut, RemuxerError> {
        self.video_codec_flv = AvcCodecId::H264 as u8; // H264 codec ID for FLV
        let video_header = RtmpCooker::gen_video_seq_header(desc, self.video_codec_flv);
        match video_header {
            Ok(header) => {
                self.video_seq_header_generated = true;
                Ok(header)
            }
            Err(e) => Err(RemuxerError::Other(e.to_string())),
        }
    }

    async fn remux_video_frame(
        &mut self,
        frame: (Bytes, u32, bool),
    ) -> Result<(BytesMut, u32), RemuxerError> {
        let (data, timestamp, is_keyframe) = frame;
        let composition_time = Some(0);
        if !self.video_seq_header_generated || !self.metadata_generated {
            return Err(RemuxerError::Other("".to_string())); // Not ready to process video
        }

        if self.base_video_ts_us == 0 {
            self.base_video_ts_us = timestamp;
        }

        let rtmp_ts_ms = ((timestamp.saturating_sub(self.base_video_ts_us)) * 1000
            / self.video_timescale) as u32;

        let cts_offset_ms = composition_time.map_or(0, |offset_us| {
            (offset_us * 1000 / self.video_timescale) as i32
        });

        let video_packet = RtmpCooker::gen_video_frame_data(
            &data,
            is_keyframe,
            cts_offset_ms,
            self.video_codec_flv,
        )?;

        Ok((video_packet, rtmp_ts_ms))
    }

    async fn remux_audio_frame(
        &mut self,
        frame: (Bytes, u32, bool),
    ) -> Result<(BytesMut, u32), RemuxerError> {
        let (data, timestamp, _is_keyframe) = frame;

        if !self.audio_seq_header_generated || !self.metadata_generated {
            return Err(RemuxerError::Other("".to_string())); // Not ready to process video
        }
        if self.base_audio_ts_us == 0 {
            self.base_audio_ts_us = timestamp;
        }
        let rtmp_ts_ms = ((timestamp.saturating_sub(self.base_audio_ts_us)) * 1000
            / self.audio_timescale) as u32;

        let audio_packet = RtmpCooker::gen_audio_frame_data(
            &data,
            self.audio_channels as u8,
            self.audio_codec_flv,
        )?;

        Ok((audio_packet, rtmp_ts_ms))
    }
}
