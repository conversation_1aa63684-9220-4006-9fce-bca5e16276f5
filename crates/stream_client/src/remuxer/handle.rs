//! Task-based remuxer handle that eliminates Send+Sync requirements
//!
//! This module implements the "Task Isolation Pattern" where remuxers
//! run in dedicated tasks and communicate via channels, making the
//! handle itself Send+Sync safe while keeping non-Send types isolated.

use bytes::{Bytes, BytesMut};
use std::collections::HashMap;
use tokio::sync::{mpsc, oneshot};
use uuid::Uuid;

use super::{Remuxer, RtmpRemuxer};
use crate::rtmp::{errors::RemuxerError, types::RemuxType};

/// Commands sent to remuxer tasks
#[derive(Debug)]
pub enum RemuxerCommand {
    GenAudioSeqHeader {
        sample_rate: u32,
        channels: u32,
        response: oneshot::Sender<Result<BytesMut, RemuxerError>>,
    },
    GenVideoSeqHeader {
        desc: Bytes,
        response: oneshot::Sender<Result<BytesMut, RemuxerError>>,
    },
    RemuxVideoFrame {
        frame: (Bytes, u32, bool),
        response: oneshot::Sender<Result<(BytesMut, u32), RemuxerError>>,
    },
    RemuxAudioFrame {
        frame: (Bytes, u32, bool),
        response: oneshot::Sender<Result<(BytesMut, u32), RemuxerError>>,
    },
    Finalize {
        response: oneshot::Sender<Result<Vec<(BytesMut, u32)>, RemuxerError>>,
    },
    Shutdown,
}

/// Send+Sync safe handle for communicating with a remuxer task
#[derive(Debug, Clone)]
pub struct RemuxerHandle {
    id: Uuid,
    command_tx: mpsc::UnboundedSender<RemuxerCommand>,
}

impl RemuxerHandle {
    /// Create a new remuxer handle and spawn background task
    pub fn spawn(remux_type: RemuxType) -> Self {
        let id = Uuid::new_v4();
        let (command_tx, command_rx) = mpsc::unbounded_channel();

        // Spawn isolated remuxer task
        tokio::spawn(async move {
            let remuxer = RtmpRemuxer::create(remux_type);
            Self::run_task(remuxer, command_rx).await;
        });

        Self { id, command_tx }
    }

    /// Run the remuxer task event loop
    async fn run_task(
        mut remuxer: Box<dyn Remuxer>,
        mut command_rx: mpsc::UnboundedReceiver<RemuxerCommand>,
    ) {
        tracing::debug!("🔄 Remuxer task started");

        while let Some(command) = command_rx.recv().await {
            match command {
                RemuxerCommand::GenAudioSeqHeader {
                    sample_rate,
                    channels,
                    response,
                } => {
                    let result = remuxer.gen_audio_seq_header(sample_rate, channels).await;
                    let _ = response.send(result);
                }
                RemuxerCommand::GenVideoSeqHeader { desc, response } => {
                    let result = remuxer.gen_video_seq_header(&desc).await;
                    let _ = response.send(result);
                }
                RemuxerCommand::RemuxVideoFrame { frame, response } => {
                    let result = remuxer.remux_video_frame(frame).await;
                    let _ = response.send(result);
                }
                RemuxerCommand::RemuxAudioFrame { frame, response } => {
                    let result = remuxer.remux_audio_frame(frame).await;
                    let _ = response.send(result);
                }
                RemuxerCommand::Finalize { response } => {
                    let result = remuxer.finalize().await;
                    let _ = response.send(result);
                    break; // End task after finalize
                }
                RemuxerCommand::Shutdown => {
                    break;
                }
            }
        }

        tracing::debug!("🛑 Remuxer task ended");
    }

    /// Generate audio sequence header
    pub async fn gen_audio_seq_header(
        &self,
        sample_rate: u32,
        channels: u32,
    ) -> Result<BytesMut, RemuxerError> {
        let (response_tx, response_rx) = oneshot::channel();

        self.command_tx
            .send(RemuxerCommand::GenAudioSeqHeader {
                sample_rate,
                channels,
                response: response_tx,
            })
            .map_err(|_| RemuxerError::Other("Remuxer task disconnected".to_string()))?;

        response_rx
            .await
            .map_err(|_| RemuxerError::Other("Response channel closed".to_string()))?
    }

    /// Generate video sequence header
    pub async fn gen_video_seq_header(&self, desc: &Bytes) -> Result<BytesMut, RemuxerError> {
        let (response_tx, response_rx) = oneshot::channel();

        self.command_tx
            .send(RemuxerCommand::GenVideoSeqHeader {
                desc: desc.clone(),
                response: response_tx,
            })
            .map_err(|_| RemuxerError::Other("Remuxer task disconnected".to_string()))?;

        response_rx
            .await
            .map_err(|_| RemuxerError::Other("Response channel closed".to_string()))?
    }

    /// Process video frame
    pub async fn remux_video_frame(
        &self,
        frame: (Bytes, u32, bool),
    ) -> Result<(BytesMut, u32), RemuxerError> {
        let (response_tx, response_rx) = oneshot::channel();

        self.command_tx
            .send(RemuxerCommand::RemuxVideoFrame {
                frame,
                response: response_tx,
            })
            .map_err(|_| RemuxerError::Other("Remuxer task disconnected".to_string()))?;

        response_rx
            .await
            .map_err(|_| RemuxerError::Other("Response channel closed".to_string()))?
    }

    /// Process audio frame
    pub async fn remux_audio_frame(
        &self,
        frame: (Bytes, u32, bool),
    ) -> Result<(BytesMut, u32), RemuxerError> {
        let (response_tx, response_rx) = oneshot::channel();

        self.command_tx
            .send(RemuxerCommand::RemuxAudioFrame {
                frame,
                response: response_tx,
            })
            .map_err(|_| RemuxerError::Other("Remuxer task disconnected".to_string()))?;

        response_rx
            .await
            .map_err(|_| RemuxerError::Other("Response channel closed".to_string()))?
    }

    /// Finalize and get remaining data
    pub async fn finalize(&self) -> Result<Vec<(BytesMut, u32)>, RemuxerError> {
        let (response_tx, response_rx) = oneshot::channel();

        self.command_tx
            .send(RemuxerCommand::Finalize {
                response: response_tx,
            })
            .map_err(|_| RemuxerError::Other("Remuxer task disconnected".to_string()))?;

        response_rx
            .await
            .map_err(|_| RemuxerError::Other("Response channel closed".to_string()))?
    }

    /// Shutdown the remuxer task
    pub fn shutdown(&self) {
        let _ = self.command_tx.send(RemuxerCommand::Shutdown);
    }

    /// Get unique handle ID
    pub fn id(&self) -> Uuid {
        self.id
    }

    /// Check if handle is connected
    pub fn is_connected(&self) -> bool {
        !self.command_tx.is_closed()
    }
}

/// Registry for managing multiple remuxer handles
#[derive(Debug, Default)]
pub struct RemuxerRegistry {
    remuxers: HashMap<Uuid, RemuxerHandle>,
}

impl RemuxerRegistry {
    pub fn new() -> Self {
        Self {
            remuxers: HashMap::new(),
        }
    }

    /// Create and register new remuxer
    pub fn create_remuxer(&mut self, remux_type: RemuxType) -> Uuid {
        let handle = RemuxerHandle::spawn(remux_type);
        let id = handle.id();
        self.remuxers.insert(id, handle);
        tracing::debug!("📝 Registered remuxer: {}", id);
        id
    }

    /// Get remuxer handle
    pub fn get_remuxer(&self, id: &Uuid) -> Option<&RemuxerHandle> {
        self.remuxers.get(id)
    }

    /// Remove and shutdown remuxer
    pub fn remove_remuxer(&mut self, id: &Uuid) -> Option<RemuxerHandle> {
        if let Some(handle) = self.remuxers.remove(id) {
            handle.shutdown();
            tracing::debug!("🗑️ Removed remuxer: {}", id);
            Some(handle)
        } else {
            None
        }
    }

    /// Shutdown all remuxers
    pub fn shutdown_all(&mut self) {
        let count = self.remuxers.len();
        for (id, handle) in self.remuxers.drain() {
            handle.shutdown();
            tracing::debug!("🛑 Shutdown remuxer: {}", id);
        }
        tracing::info!("🛑 Shutdown {} remuxers", count);
    }

    /// Get active remuxer count
    pub fn count(&self) -> usize {
        self.remuxers.len()
    }

    /// Get all remuxer IDs
    pub fn ids(&self) -> Vec<Uuid> {
        self.remuxers.keys().copied().collect()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::rtmp::types::RemuxType;

    #[tokio::test]
    async fn test_remuxer_handle_creation() {
        let handle = RemuxerHandle::spawn(RemuxType::MoqRemuxer);
        assert!(!handle.id().is_nil());
        assert!(handle.is_connected());
        handle.shutdown();
    }

    #[tokio::test]
    async fn test_remuxer_registry() {
        let mut registry = RemuxerRegistry::new();
        let id = registry.create_remuxer(RemuxType::MoqRemuxer);

        assert!(registry.get_remuxer(&id).is_some());
        assert_eq!(registry.count(), 1);
        assert!(registry.ids().contains(&id));

        registry.remove_remuxer(&id);
        assert_eq!(registry.count(), 0);
    }

    #[tokio::test]
    async fn test_shutdown_all() {
        let mut registry = RemuxerRegistry::new();
        let _id1 = registry.create_remuxer(RemuxType::MoqRemuxer);
        let _id2 = registry.create_remuxer(RemuxType::MoqRemuxer);

        assert_eq!(registry.count(), 2);
        registry.shutdown_all();
        assert_eq!(registry.count(), 0);
    }
}
