use super::connection::Connection;
use super::stats::Stats;
use super::types::{ClientState, FrameDataReceiver, PushClientError};
use rml_rtmp::sessions::{ClientSession, ClientSessionConfig};
use std::sync::Arc;
use std::time::Duration;
use tokio::net::TcpStream;
use tokio::sync::watch;
use tracing::{debug, error, info, warn};
use url::Url;

const LOOP_TIMEOUT: Duration = Duration::from_secs(60);

pub struct RtmpSession {
    pub frame_receiver: FrameDataReceiver,
    pub rtmp_url: String,
    pub state: ClientState,
    pub stream_key: String,
    pub stream: Option<TcpStream>,
    pub session: Option<ClientSession>,
    pub connection: Option<Connection>,
    pub stats: Arc<Stats>,
    pub state_sender: Option<watch::Sender<ClientState>>,
    // pub frame_buffer: VecDeque<FrameData>, // Optional: Buffer for frames if needed
}

impl RtmpSession {
    pub async fn new(rtmp_url: String, frame_receiver: FrameDataReceiver) -> Self {
        Self {
            frame_receiver,
            rtmp_url,
            state: ClientState::Idle,
            stream_key: String::new(),
            stream: None,
            session: None,
            connection: None,
            stats: Arc::new(Stats::new()),
            state_sender: None,
        }
    }

    pub async fn new_with_stats(
        rtmp_url: String,
        frame_receiver: FrameDataReceiver,
        stats: Arc<Stats>,
        state_sender: watch::Sender<ClientState>
    ) -> Self {
        Self {
            frame_receiver,
            rtmp_url,
            state: ClientState::Idle,
            stream_key: String::new(),
            stream: None,
            session: None,
            connection: None,
            stats,
            state_sender: Some(state_sender),
        }
    }

    pub async fn run(&mut self) -> Result<(), PushClientError> {
        info!("PushClient run loop started.");

        loop {
            // Use a simple approach without complex borrowing in select!
            tokio::select! {
                // Handle frame reception
                frame_opt = self.frame_receiver.recv() => {
                    match frame_opt {
                        Some(frame) => {
                            if self.state != ClientState::Publishing {
                                warn!("Received frame but not in Publishing state (state: {:?}). Dropping.", self.state);
                                continue;
                            }

                            if let Err(e) = self.publish(frame).await {
                                error!("Error publishing frame: {}", e);
                                return Err(e);
                            }
                        }
                        None => {
                            info!("Frame receiver channel closed. Shutting down client.");
                            self.state = ClientState::Closed;
                            if let Err(e) = self.unpublish().await {
                                warn!("Error during unpublish: {}", e);
                            }
                            if let Some(conn) = self.connection.as_mut() {
                                if let Err(e) = conn.shutdown().await {
                                    warn!("Error shutting down connection: {}", e);
                                }
                            }
                            return Ok(());
                        }
                    }
                },

                // Timeout for keep-alives or detecting stalls
                _ = tokio::time::sleep(LOOP_TIMEOUT) => {
                    debug!("Client loop timeout reached. State: {:?}", self.state);
                    if self.state == ClientState::Publishing {
                        if let Some(session) = self.session.as_mut() {
                            if let Err(e) = session.send_ping_request() {
                                error!("Failed to send ping: {}", e);
                            }
                        }
                    } else if self.state == ClientState::SendingConnect || self.state == ClientState::SendingPublish {
                        warn!("Client is in state {:?} for too long. Possible issue.", self.state);
                        self.state = ClientState::Error("Timeout during connection/publish".to_string());
                        return Err(PushClientError::Timeout("Connection/Publish timeout".to_string()));
                    }
                }
            }

            let data = self.connection_mut()?.read_data().await?;
            let data_vec = data.to_vec();
            if let Err(e) = self.process_bytes(&data_vec).await {
                error!("Error processing bytes: {}", e);
                self.state = ClientState::Error(e.to_string());
                return Err(e);
            }

            if self.state == ClientState::Closed || matches!(self.state, ClientState::Error(_)) {
                info!("Client stopping due to state: {:?}", self.state);
                break;
            }
        }
        Ok(())
    }

    pub async fn create(&mut self) -> Result<(), PushClientError> {
        info!("Initializing RTMP PushClient for URL: {}", self.rtmp_url);

        let url = Url::parse(&self.rtmp_url)?;
        let addr = url.socket_addrs(|| Some(1935))?;
        let (app_name, _stream_key) = self.parse_url(url).await?;

        info!("Connecting to RTMP server...");
        self.set_state(ClientState::Connecting);
        let stream = TcpStream::connect(&*addr).await?;
        info!("TCP connection established.");

        // Start tracking connection stats
        self.stats.start_connection();

        let mut connection = Connection::new(stream);

        info!("Performing RTMP handshake...");
        self.set_state(ClientState::Handshaking);
        connection.perform_handshake().await?;
        self.connection = Some(connection);
        self.set_state(ClientState::Connected);
        info!("RTMP handshake completed.");

        info!("Initializing RTMP ClientSession...");
        let config = ClientSessionConfig::new();
        let (mut session, initial_results) = ClientSession::new(config)?;
        info!("ClientSession initialized.");

        info!("Sending initial 'connect' command...");
        self.set_state(ClientState::SendingConnect);
        let connect_result = session.request_connection(app_name.clone())?;
        self.session = Some(session);
        info!("Connect command sent.");

        let mut initial_packets = Vec::new();
        initial_packets.push(connect_result);
        initial_packets.extend(initial_results);

        for result in initial_packets {
            if let Err(e) = self.handle_result(result).await {
                error!("Error processing initial result: {}", e);
                return Err(e);
            }
        }

        info!("RTMP PushClient initialized successfully.");
        Ok(())
    }

    pub async fn process_bytes(&mut self, data: &[u8]) -> Result<(), PushClientError> {
        if data.is_empty() {
            // Should be handled by read_data returning error on EOF, but double-check
            info!("Read 0 bytes, assuming connection closed.");
            self.state = ClientState::Closed;
            return Err(PushClientError::ConnectionFailed(
                "Connection closed by peer".to_string(),
            ));
        }

        // Track received data
        self.stats.record_bytes_received(data.len());

        let bytes_consumed;
        let session = self
            .session
            .as_mut()
            .ok_or_else(|| PushClientError::SessionError("No active RTMP session".to_string()))?;
        match session.handle_input(data) {
            Ok(results) => {
                bytes_consumed = data.len();
                for result in results {
                    if let Err(e) = self.handle_result(result).await {
                        error!("Error processing session result: {}", e);
                        return Err(e);
                    }
                }
            }
            Err(e) => {
                error!("RTMP session error on handle_input: {}", e);
                self.stats.record_connection_error();
                self.state = ClientState::Error(e.to_string());
                bytes_consumed = data.len();
                // return Err(PushClientError::SessionError(e.to_string())); // Decide if error is fatal
            }
        }
        self.connection_mut()?.consume_data(bytes_consumed);
        Ok(())
    }
}
