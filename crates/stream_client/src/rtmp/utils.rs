use super::StatsSnapshot;
use super::connection::Connection;
use super::session::RtmpSession;
use super::types::{ClientState, FrameData, PushClientError};
use rml_rtmp::sessions::{
    ClientSession, ClientSessionEvent, ClientSessionResult, PublishRequestType,
};
use rml_rtmp::time::RtmpTimestamp;
use tracing::{debug, error, info, trace, warn};
use url::Url;

impl RtmpSession {
    /// Returns the current state of the self.
    pub fn get_state(&self) -> ClientState {
        self.state.clone()
    }

    /// Sets the state of the self.
    pub fn set_state(&mut self, new_state: ClientState) {
        debug!("Client state changing: {:?} -> {:?}", self.state, new_state);
        self.state = new_state.clone();

        // Notify state change if we have a sender
        if let Some(sender) = &self.state_sender {
            let _ = sender.send(new_state); // Ignore errors if receiver is dropped
        }
    }

    /// Returns a mutable reference to the underlying ClientSession.
    /// Use with caution, prefer dedicated methods if possible.
    pub fn session_mut(&mut self) -> Result<&mut ClientSession, PushClientError> {
        self.session
            .as_mut()
            .ok_or_else(|| PushClientError::SessionError("No active RTMP session".to_string()))
    }

    /// Returns a mutable reference to the underlying Connection.
    /// Use with caution.
    pub fn connection_mut(&mut self) -> Result<&mut Connection, PushClientError> {
        self.connection
            .as_mut()
            .ok_or_else(|| PushClientError::ConnectionFailed("No active connection".to_string()))
    }

    /// Returns the stream key.
    pub fn stream_key(&self) -> &String {
        &self.stream_key
    }

    /// Get current statistics snapshot
    pub fn get_stats(&self) -> StatsSnapshot {
        self.stats.snapshot()
    }

    /// Reset statistics
    pub fn reset_stats(&self) {
        self.stats.reset()
    }

    pub async fn parse_url(&mut self, url: Url) -> Result<(String, String), PushClientError> {
        let mut path_segments = url
            .path_segments()
            .ok_or(PushClientError::MissingAppName)?
            .filter(|s| !s.is_empty());

        let app_name = path_segments
            .next()
            .map(|s| s.to_string())
            .ok_or(PushClientError::MissingAppName)?;

        let stream_key = path_segments
            .next()
            .map(|s| s.to_string())
            .ok_or(PushClientError::MissingStreamKey)?;

        self.stream_key = stream_key.clone(); // Store stream key in the client

        Ok((app_name, stream_key))
    }

    pub async fn publish(&mut self, frame: FrameData) -> Result<(), PushClientError> {
        let timestamp = RtmpTimestamp::new(frame.timestamp_ms());
        let frame_size = frame.len();
        let is_video = matches!(frame, FrameData::Video { .. });

        let result_opt = match frame {
            FrameData::Video { payload, .. } => {
                self.stats.record_frame_sent(true);
                self.session_mut()?
                    .publish_video_data(payload, timestamp, true)
                    .ok()
            }
            FrameData::Audio { payload, .. } => {
                self.stats.record_frame_sent(false);
                self.session_mut()?
                    .publish_audio_data(payload, timestamp, true)
                    .ok()
            }
            FrameData::Metadata { metadata, .. } => {
                info!("Publishing metadata");
                self.session_mut()?.publish_metadata(&metadata.into()).ok()
            }
        };

        if let Some(result) = result_opt {
            if let Err(e) = self.handle_result(result).await {
                error!("Error processing media publish result: {}", e);
                self.stats.record_publish_error();
                return Err(e);
            }
            // Record successful data transmission
            self.stats.record_bytes_sent(frame_size);
        } else {
            self.stats.record_publish_error();
        }

        trace!(
            "Published {} frame of {} bytes",
            if is_video { "video" } else { "audio" },
            frame_size
        );
        Ok(())
    }

    pub async fn unpublish(&mut self) -> Result<(), PushClientError> {
        info!("Unpublishing stream...");

        match self.session_mut()?.stop_publishing() {
            Ok(unpublish_result) => {
                self.set_state(ClientState::Idle);

                for result in unpublish_result {
                    if let Err(e) = self.handle_result(result).await {
                        error!("Error processing unpublish result: {}", e);
                        self.stats.record_unpublish_error();
                        return Err(e);
                    }
                }

                info!("Stream publishing stopped successfully.");
                Ok(())
            }
            Err(e) => {
                error!("Failed to stop publishing: {}", e);
                self.stats.record_unpublish_error();
                Err(PushClientError::RtmpSession(e))
            }
        }
    }

    pub async fn handle_result(
        &mut self,
        result: ClientSessionResult,
    ) -> Result<(), PushClientError> {
        trace!("PushClient: Handling Client Session Result: {:?}", result);
        match result {
            ClientSessionResult::OutboundResponse(packet) => {
                let packet_size = packet.bytes.len();
                match self.connection_mut()?.write_packet(&packet.bytes).await {
                    Ok(_) => {
                        self.stats.record_bytes_sent(packet_size);
                        trace!("Sent packet of {} bytes", packet_size);
                    }
                    Err(e) => {
                        self.stats.record_connection_error();
                        return Err(e);
                    }
                }
            }
            ClientSessionResult::RaisedEvent(event) => {
                trace!("PushClient: Received event: {:?}", event);
                self.handle_event(event).await?;
            }
            ClientSessionResult::UnhandleableMessageReceived(msg) => {
                warn!("PushClient: Received unhandleable message: {:?}", msg);
            }
        }
        Ok(())
    }

    async fn handle_event(&mut self, event: ClientSessionEvent) -> Result<(), PushClientError> {
        info!("PushClient: Handling Client Session Event: {:?}", event);
        let current_state = self.get_state(); // Assuming a getter method exists

        match event {
            ClientSessionEvent::ConnectionRequestAccepted => {
                if current_state == ClientState::SendingConnect {
                    info!("PushClient: RTMP Connection Accepted. Requesting publish...");
                    self.set_state(ClientState::SendingPublish);

                    let stream_key = self.stream_key().clone();
                    let publish_result = self
                        .session_mut()?
                        .request_publishing(stream_key, PublishRequestType::Live)?;

                    if let ClientSessionResult::OutboundResponse(ref p) = publish_result {
                        self.connection_mut()?.write_packet(&p.bytes).await?; // Use &p.bytes
                        info!("PushClient: Publish request sent.");
                    } else {
                        warn!(
                            "PushClient: Unexpected result type after requesting publish: {:?}",
                            publish_result
                        );
                    }
                } else {
                    warn!(
                        "PushClient: Received ConnectionRequestAccepted in unexpected state: {:?}",
                        current_state
                    );
                }
            }
            ClientSessionEvent::PublishRequestAccepted => {
                if current_state == ClientState::SendingPublish {
                    info!("PushClient: Publish Accepted. Ready to send media data.");
                    self.set_state(ClientState::Publishing);
                    // Optional: Send any buffered frames now (logic would need client access)
                    // while let Some(frame) = self.frame_buffer.pop_front() { ... process frame ... }
                } else {
                    warn!(
                        "PushClient: Received PublishRequestAccepted in unexpected state: {:?}",
                        current_state
                    );
                }
            }
            ClientSessionEvent::ConnectionRequestRejected { description } => {
                error!("PushClient: RTMP Connection rejected: {}", description);
                self.set_state(ClientState::Error(format!(
                    "Connection rejected: {}",
                    description
                )));
                return Err(PushClientError::ConnectFailed);
            }
            ClientSessionEvent::AcknowledgementReceived { bytes_received } => {
                debug!("PushClient: Server acknowledged {} bytes", bytes_received);
                // Can be used for flow control if needed
            }
            ClientSessionEvent::PingResponseReceived { timestamp } => {
                debug!(
                    "PushClient: Ping response received (timestamp: {:?})",
                    timestamp
                ); // Use {:?} for RtmpTimestamp
                // Can be used to calculate RTT
            }
            _ => {
                debug!("PushClient: Unhandled client session event: {:?}", event);
            }
        }
        Ok(())
    }
}
