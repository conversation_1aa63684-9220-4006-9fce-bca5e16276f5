use bytesio::{
    bits_errors::BitError,
    bytes_errors::{BytesReadError, BytesWriteError},
};
use thiserror::Error;

use tokio::sync::oneshot::error::RecvError;
use xflv::{amf0::Amf0WriteError, errors::FlvMuxerError};

use rml_rtmp::sessions::ClientSessionError;
use std::string::ParseError;
use tokio::sync::mpsc::error::SendError;
use url::ParseError as UrlParseError;

use super::types::FrameData; // Alias to avoid conflict with PushError::UrlParse

/// Errors that can occur during the RTMP push process.
#[derive(Debug, Error)]
pub enum PushClientError {
    #[error("Network I/O error: {0}")]
    Io(#[from] std::io::Error),

    #[error("URL parsing error: {0}")]
    UrlParse(#[from] UrlParseError),

    #[error("RTMP session error: {0}")]
    SessionError(String), // Consider using rml_rtmp::sessions::ClientSessionError if possible

    #[error("Connection failed: {0}")]
    ConnectionFailed(String),

    #[error("Handshake failed: {0}")]
    HandshakeFailed(String),

    #[error("RTMP Connect command failed")]
    ConnectFailed,

    #[error("RTMP Publish command failed")]
    PublishFailed,

    #[error("Metadata publishing failed")]
    MetadataPublishFailed,

    #[error("Media data publishing failed")]
    MediaPublishFailed,

    #[error("Receiver channel closed")]
    ReceiverClosed,

    #[error("Invalid state for operation: {0}")]
    InvalidState(String),

    #[error("Missing stream key in URL")]
    MissingStreamKey,

    #[error("Missing application name in URL")]
    MissingAppName,

    #[error("Timeout during {0}")]
    Timeout(String),

    #[error("Parse error: {0}")]
    ParseError(#[from] ParseError),

    #[error("RTMP client session error: {0}")]
    RtmpSession(#[from] ClientSessionError),

    #[error("Failed to send message: {0}")]
    SendError(String),
}

impl From<SendError<FrameData>> for PushClientError {
    fn from(err: SendError<FrameData>) -> Self {
        PushClientError::SendError(err.to_string())
    }
}

#[derive(Debug, Error)]
pub enum RemuxerError {
    #[error("I/O error: {0}")]
    Io(#[from] std::io::Error),
    #[error("amf write error: {0}")]
    Amf0Write(Amf0WriteError),
    #[error("bytes read error: {0}")]
    BytesRead(BytesReadError),
    #[error("bytes write error: {0}")]
    BytesWrite(BytesWriteError),
    #[error("mpeg avc error: {0}")]
    MpegAvc(Mpeg4AvcHevcError),
    #[error("flv muxer error: {0}")]
    FlvMuxer(FlvMuxerError),
    #[error("receive error: {0}")]
    Recv(#[from] RecvError),
    #[error("Invalid NALU data")]
    InvalidNaluData,
    #[error("Missing SPS or PPS NALUs for sequence header")]
    MissingSpsPps,
    #[error("other error: {0}")]
    Other(String),
    #[error("Invalid parameter: {0}")]
    InvalidParam(String),
    #[error("Invalid codec id: {0}")]
    UnsupportedAudioCodec(String),
    #[error("Unsupported codec id: {0}")]
    UnsupportedVideoCodec(String),
}

impl From<BytesReadError> for RemuxerError {
    fn from(e: BytesReadError) -> Self {
        RemuxerError::BytesRead(e)
    }
}
impl From<BytesWriteError> for RemuxerError {
    fn from(e: BytesWriteError) -> Self {
        RemuxerError::BytesWrite(e)
    }
}
impl From<Mpeg4AvcHevcError> for RemuxerError {
    fn from(e: Mpeg4AvcHevcError) -> Self {
        RemuxerError::MpegAvc(e)
    }
}
impl From<FlvMuxerError> for RemuxerError {
    fn from(e: FlvMuxerError) -> Self {
        RemuxerError::FlvMuxer(e)
    }
}

impl From<Amf0WriteError> for RemuxerError {
    fn from(e: Amf0WriteError) -> Self {
        RemuxerError::Amf0Write(e)
    }
}

impl From<String> for RemuxerError {
    fn from(s: String) -> Self {
        RemuxerError::Other(s)
    }
}

pub type Result<T> = std::result::Result<T, RemuxerError>;

#[derive(Debug, Error)]
pub enum Mpeg4AvcHevcError {
    #[error("bytes read error:{}", _0)]
    BytesReadError(BytesReadError),
    #[error("bytes write error:{}", _0)]
    BytesWriteError(BytesWriteError),
    #[error("bits error:{}", _0)]
    BitError(BitError),

    #[error("there is not enough bits to read")]
    NotEnoughBitsToRead,
    #[error("should not come here")]
    ShouldNotComeHere,
    #[error("the sps nal unit type is not correct")]
    SPSNalunitTypeNotCorrect,
    #[error("not supported sampling frequency")]
    NotSupportedSamplingFrequency,
    #[error("Ivalid NAL unit lenght")]
    InvalidNaluLength,
    #[error("Invalid parameter: {0}")]
    InvalidParam(String),
}

impl From<BytesReadError> for Mpeg4AvcHevcError {
    fn from(error: BytesReadError) -> Self {
        Mpeg4AvcHevcError::BytesReadError(error)
    }
}

impl From<BytesWriteError> for Mpeg4AvcHevcError {
    fn from(error: BytesWriteError) -> Self {
        Mpeg4AvcHevcError::BytesWriteError(error)
    }
}
