use std::sync::atomic::{AtomicUsize, AtomicU64, Ordering};
use std::sync::Arc;
use std::time::{Duration, Instant};
use serde::{Serialize, Deserialize};

/// Thread-safe statistics tracking for RTMP operations
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct Stats {
    inner: Arc<StatsInner>,
}

#[derive(Debug)]
struct StatsInner {
    // Connection stats
    bytes_sent: AtomicUsize,
    bytes_received: AtomicUsize,
    
    // Frame stats
    frames_sent: AtomicUsize,
    frames_received: AtomicUsize,
    video_frames_sent: AtomicUsize,
    audio_frames_sent: AtomicUsize,
    
    // Error stats
    connection_errors: AtomicUsize,
    publish_errors: AtomicUsize,
    unpublish_errors: AtomicUsize,
    
    // Timing stats
    connection_start_time: std::sync::Mutex<Option<Instant>>,
    last_frame_time: std::sync::Mutex<Option<Instant>>,
    
    // Bitrate tracking
    bitrate_bytes_window: AtomicU64,
    bitrate_window_start: std::sync::Mutex<Option<Instant>>,
}

/// Snapshot of statistics at a point in time
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct StatsSnapshot {
    pub bytes_sent: usize,
    pub bytes_received: usize,
    pub frames_sent: usize,
    pub frames_received: usize,
    pub video_frames_sent: usize,
    pub audio_frames_sent: usize,
    pub connection_errors: usize,
    pub publish_errors: usize,
    pub unpublish_errors: usize,
    pub uptime_seconds: Option<u64>,
    pub estimated_bitrate_kbps: Option<f64>,
    pub frames_per_second: Option<f64>,
}

impl Stats {
    pub fn new() -> Self {
        Self {
            inner: Arc::new(StatsInner {
                bytes_sent: AtomicUsize::new(0),
                bytes_received: AtomicUsize::new(0),
                frames_sent: AtomicUsize::new(0),
                frames_received: AtomicUsize::new(0),
                video_frames_sent: AtomicUsize::new(0),
                audio_frames_sent: AtomicUsize::new(0),
                connection_errors: AtomicUsize::new(0),
                publish_errors: AtomicUsize::new(0),
                unpublish_errors: AtomicUsize::new(0),
                connection_start_time: std::sync::Mutex::new(None),
                last_frame_time: std::sync::Mutex::new(None),
                bitrate_bytes_window: AtomicU64::new(0),
                bitrate_window_start: std::sync::Mutex::new(None),
            }),
        }
    }

    /// Record bytes sent over the connection
    pub fn record_bytes_sent(&self, bytes: usize) {
        self.inner.bytes_sent.fetch_add(bytes, Ordering::Relaxed);
        self.update_bitrate_window(bytes);
    }

    /// Record bytes received from the connection
    pub fn record_bytes_received(&self, bytes: usize) {
        self.inner.bytes_received.fetch_add(bytes, Ordering::Relaxed);
    }

    /// Record a frame being sent
    pub fn record_frame_sent(&self, is_video: bool) {
        self.inner.frames_sent.fetch_add(1, Ordering::Relaxed);
        if is_video {
            self.inner.video_frames_sent.fetch_add(1, Ordering::Relaxed);
        } else {
            self.inner.audio_frames_sent.fetch_add(1, Ordering::Relaxed);
        }

        // Update last frame time
        let mut last_frame_time = self.inner.last_frame_time.lock().unwrap();
        *last_frame_time = Some(Instant::now());
    }

    /// Record a frame being received
    pub fn record_frame_received(&self) {
        self.inner.frames_received.fetch_add(1, Ordering::Relaxed);
    }

    /// Record a connection error
    pub fn record_connection_error(&self) {
        self.inner.connection_errors.fetch_add(1, Ordering::Relaxed);
    }

    /// Record a publish error
    pub fn record_publish_error(&self) {
        self.inner.publish_errors.fetch_add(1, Ordering::Relaxed);
    }

    /// Record an unpublish error
    pub fn record_unpublish_error(&self) {
        self.inner.unpublish_errors.fetch_add(1, Ordering::Relaxed);
    }

    /// Mark the start of a connection
    pub fn start_connection(&self) {
        let mut start_time = self.inner.connection_start_time.lock().unwrap();
        *start_time = Some(Instant::now());

        // Reset bitrate window
        let mut window_start = self.inner.bitrate_window_start.lock().unwrap();
        *window_start = Some(Instant::now());
        self.inner.bitrate_bytes_window.store(0, Ordering::Relaxed);
    }

    /// Get a snapshot of current statistics
    pub fn snapshot(&self) -> StatsSnapshot {
        let uptime_seconds = {
            let start_time = self.inner.connection_start_time.lock().unwrap();
            start_time.map(|start| start.elapsed().as_secs())
        };

        let estimated_bitrate_kbps = self.calculate_bitrate();
        let frames_per_second = self.calculate_fps();

        StatsSnapshot {
            bytes_sent: self.inner.bytes_sent.load(Ordering::Relaxed),
            bytes_received: self.inner.bytes_received.load(Ordering::Relaxed),
            frames_sent: self.inner.frames_sent.load(Ordering::Relaxed),
            frames_received: self.inner.frames_received.load(Ordering::Relaxed),
            video_frames_sent: self.inner.video_frames_sent.load(Ordering::Relaxed),
            audio_frames_sent: self.inner.audio_frames_sent.load(Ordering::Relaxed),
            connection_errors: self.inner.connection_errors.load(Ordering::Relaxed),
            publish_errors: self.inner.publish_errors.load(Ordering::Relaxed),
            unpublish_errors: self.inner.unpublish_errors.load(Ordering::Relaxed),
            uptime_seconds,
            estimated_bitrate_kbps,
            frames_per_second,
        }
    }

    /// Reset all statistics
    pub fn reset(&self) {
        self.inner.bytes_sent.store(0, Ordering::Relaxed);
        self.inner.bytes_received.store(0, Ordering::Relaxed);
        self.inner.frames_sent.store(0, Ordering::Relaxed);
        self.inner.frames_received.store(0, Ordering::Relaxed);
        self.inner.video_frames_sent.store(0, Ordering::Relaxed);
        self.inner.audio_frames_sent.store(0, Ordering::Relaxed);
        self.inner.connection_errors.store(0, Ordering::Relaxed);
        self.inner.publish_errors.store(0, Ordering::Relaxed);
        self.inner.unpublish_errors.store(0, Ordering::Relaxed);

        let mut start_time = self.inner.connection_start_time.lock().unwrap();
        *start_time = None;

        let mut last_frame_time = self.inner.last_frame_time.lock().unwrap();
        *last_frame_time = None;

        let mut window_start = self.inner.bitrate_window_start.lock().unwrap();
        *window_start = None;
        self.inner.bitrate_bytes_window.store(0, Ordering::Relaxed);
    }

    fn update_bitrate_window(&self, bytes: usize) {
        const BITRATE_WINDOW: Duration = Duration::from_secs(10); // 10-second window

        let now = Instant::now();
        let mut window_start = self.inner.bitrate_window_start.lock().unwrap();
        
        // Reset window if it's too old or doesn't exist
        if let Some(start) = *window_start {
            if now.duration_since(start) > BITRATE_WINDOW {
                *window_start = Some(now);
                self.inner.bitrate_bytes_window.store(bytes as u64, Ordering::Relaxed);
            } else {
                self.inner.bitrate_bytes_window.fetch_add(bytes as u64, Ordering::Relaxed);
            }
        } else {
            *window_start = Some(now);
            self.inner.bitrate_bytes_window.store(bytes as u64, Ordering::Relaxed);
        }
    }

    fn calculate_bitrate(&self) -> Option<f64> {
        const BITRATE_WINDOW: Duration = Duration::from_secs(10);

        let window_start = self.inner.bitrate_window_start.lock().unwrap();
        if let Some(start) = *window_start {
            let elapsed = start.elapsed();
            if elapsed >= Duration::from_secs(1) {
                let bytes = self.inner.bitrate_bytes_window.load(Ordering::Relaxed);
                let elapsed_seconds = elapsed.as_secs_f64();
                let window_seconds = elapsed_seconds.min(BITRATE_WINDOW.as_secs_f64());
                
                // Convert bytes per second to kilobits per second
                let bitrate_kbps = (bytes as f64 * 8.0) / (window_seconds * 1000.0);
                return Some(bitrate_kbps);
            }
        }
        None
    }

    fn calculate_fps(&self) -> Option<f64> {
        let connection_start = self.inner.connection_start_time.lock().unwrap();
        if let Some(start) = *connection_start {
            let elapsed = start.elapsed();
            if elapsed >= Duration::from_secs(1) {
                let frames = self.inner.frames_sent.load(Ordering::Relaxed);
                let fps = frames as f64 / elapsed.as_secs_f64();
                return Some(fps);
            }
        }
        None
    }
}

impl Default for Stats {
    fn default() -> Self {
        Self::new()
    }
}

impl Default for StatsSnapshot {
    fn default() -> Self {
        Self {
            bytes_sent: 0,
            bytes_received: 0,
            frames_sent: 0,
            frames_received: 0,
            video_frames_sent: 0,
            audio_frames_sent: 0,
            connection_errors: 0,
            publish_errors: 0,
            unpublish_errors: 0,
            uptime_seconds: None,
            estimated_bitrate_kbps: None,
            frames_per_second: None,
        }
    }
}
