use bytes::Bytes;
use rml_rtmp::sessions::{ClientSessionError, StreamMetadata};
use uuid::Uuid;
use std::string::ParseError;
use thiserror::Error;
use tokio::sync::mpsc::{self, error::SendError};
use url::ParseError as UrlParseError; // Alias to avoid conflict with PushError::UrlParse
use serde::Serialize;

/// Errors that can occur during the RTMP push process.
#[derive(Debug, Error)]
pub enum PushClientError {
    #[error("Network I/O error: {0}")]
    Io(#[from] std::io::Error),

    #[error("URL parsing error: {0}")]
    UrlParse(#[from] UrlParseError),

    #[error("RTMP session error: {0}")]
    SessionError(String), // Consider using rml_rtmp::sessions::ClientSessionError if possible

    #[error("Connection failed: {0}")]
    ConnectionFailed(String),

    #[error("Handshake failed: {0}")]
    HandshakeFailed(String),

    #[error("RTMP Connect command failed")]
    ConnectFailed,

    #[error("RTMP Publish command failed")]
    PublishFailed,

    #[error("Metadata publishing failed")]
    MetadataPublishFailed,

    #[error("Media data publishing failed")]
    MediaPublishFailed,

    #[error("Receiver channel closed")]
    ReceiverClosed,

    #[error("Invalid state for operation: {0}")]
    InvalidState(String),

    #[error("Missing stream key in URL")]
    MissingStreamKey,

    #[error("Missing application name in URL")]
    MissingAppName,

    #[error("Timeout during {0}")]
    Timeout(String),

    #[error("Parse error: {0}")]
    ParseError(#[from] ParseError),

    #[error("RTMP client session error: {0}")]
    RtmpSession(#[from] ClientSessionError),

    #[error("Failed to send message: {0}")]
    SendError(String),
}

impl From<SendError<FrameData>> for PushClientError {
    fn from(err: SendError<FrameData>) -> Self {
        PushClientError::SendError(err.to_string())
    }
}

pub type FrameDataSender = mpsc::UnboundedSender<FrameData>;
pub type FrameDataReceiver = mpsc::UnboundedReceiver<FrameData>;
pub type Result<T> = std::result::Result<T, PushClientError>;

/// Represents the different states of the RTMP push client connection.
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum ClientState {
    Idle,
    Connecting,
    Handshaking,
    Connected, // After handshake, before RTMP connect
    SendingConnect,
    SendingPublish,
    Publishing, // Ready to send media
    Error(String),
    Closed,
}

#[derive(PartialEq, Debug, Clone)]
pub struct Metadata {
    pub video_width: Option<u32>,
    pub video_height: Option<u32>,
    pub video_codec_id: Option<u32>,
    pub video_frame_rate: Option<f32>,
    pub video_bitrate_kbps: Option<u32>,
    pub audio_codec_id: Option<u32>,
    pub audio_bitrate_kbps: Option<u32>,
    pub audio_sample_rate: Option<u32>,
    pub audio_channels: Option<u32>,
    pub audio_is_stereo: Option<bool>,
    pub encoder: Option<String>,
}

impl Into<StreamMetadata> for Metadata {
    fn into(self) -> StreamMetadata {
        StreamMetadata {
            video_width: self.video_width,
            video_height: self.video_height,
            video_codec_id: self.video_codec_id,
            video_frame_rate: self.video_frame_rate,
            video_bitrate_kbps: self.video_bitrate_kbps,
            audio_codec_id: self.audio_codec_id,
            audio_bitrate_kbps: self.audio_bitrate_kbps,
            audio_sample_rate: self.audio_sample_rate,
            audio_channels: self.audio_channels,
            audio_is_stereo: self.audio_is_stereo,
            encoder: self.encoder,
        }
    }
}

#[derive(Clone, Debug, PartialEq)]
pub enum FrameData {
    Video { timestamp: u32, payload: Bytes },
    Audio { timestamp: u32, payload: Bytes },
    Metadata { metadata: Metadata, source_id: Uuid },
}

impl FrameData {
    pub fn len(&self) -> usize {
        match self {
            FrameData::Video { payload, .. } => payload.len(),
            FrameData::Audio { payload, .. } => payload.len(),
            FrameData::Metadata { .. } => 0, // Metadata doesn't have a length
        }
    }

    pub fn timestamp_ms(&self) -> u32 {
        match self {
            FrameData::Video { timestamp, .. } => *timestamp,
            FrameData::Audio { timestamp, .. } => *timestamp,
            FrameData::Metadata { .. } => 0, // Metadata often sent at timestamp 0
        }
    }
    pub fn source_id(&self) -> Uuid {
        match self {
            FrameData::Video { .. } => Uuid::new_v4(),
            FrameData::Audio { .. } => Uuid::new_v4(),
            FrameData::Metadata { source_id, .. } => *source_id,
        }
    }

}

#[derive(Debug, Clone, Serialize, Default)]
pub enum SoundFormat {
    #[default]
    AAC = 10,
    OPUS = 13,
}

#[derive(Debug, Clone, Serialize, Default)]
pub enum AvcCodecId {
    #[default]
    UNKNOWN = 0,
    H264 = 7,
    HEVC = 12,
}

#[derive(Debug, Clone, Serialize, Default)]
pub enum FrameType {
    #[default]
    KeyFrame = 1,
    InterFrame = 2,
}

#[derive(Debug, Clone, Serialize, Default)]
pub enum AvcPacketType {
    #[default]
    SequenceHeader = 0,
    Nalu = 1,
    EndOfSequence = 2,
}

pub enum RemuxType {
    MoqRemuxer
}

