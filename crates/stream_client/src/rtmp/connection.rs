use super::types::PushClientError;
use std::time::Duration;
use tokio::io::{AsyncReadExt, AsyncWriteExt};
use tokio::net::TcpStream;
use tokio::time::timeout;
use tracing::{debug, error, info, trace, warn};

use rml_rtmp::handshake::{Handshake, HandshakeProcessResult, PeerType};

const HANDSHAKE_TIMEOUT: Duration = Duration::from_secs(10);
const HANDSHAKE_BUFFER_SIZE: usize = 4096; // Should be enough for S0+S1+S2

const READ_TIMEOUT: Duration = Duration::from_secs(30);
const WRITE_TIMEOUT: Duration = Duration::from_secs(10);
const READ_BUFFER_SIZE: usize = 4096;

#[derive(Debug)]
pub struct Connection {
    stream: TcpStream,
    read_buffer: Vec<u8>,
    bytes_read_since_last_processed: usize,
}

impl Connection {
    pub fn new(stream: TcpStream) -> Self {
        Connection {
            stream,
            read_buffer: vec![0; READ_BUFFER_SIZE],
            bytes_read_since_last_processed: 0,
        }
    }

    pub async fn read_data(&mut self) -> Result<&[u8], PushClientError> {
        // Reset if previous data was fully processed
        if self.bytes_read_since_last_processed == 0 {
            self.bytes_read_since_last_processed = 0; // Ensure it's zero
        } else {
            // If there were unprocessed bytes, shift them to the beginning
            // This scenario is less likely if handle_input processes all bytes,
            // but good practice for potential partial reads/processing.
            warn!(
                "Shifting {} unprocessed bytes in read buffer",
                self.bytes_read_since_last_processed
            );
            self.read_buffer
                .copy_within(self.bytes_read_since_last_processed.., 0);
        }

        let buffer_start_index = self.bytes_read_since_last_processed;
        let available_buffer = &mut self.read_buffer[buffer_start_index..];

        match timeout(READ_TIMEOUT, self.stream.read(available_buffer)).await {
            Ok(Ok(0)) => {
                error!("Connection closed by peer while reading");
                Err(PushClientError::ConnectionFailed(
                    "Connection closed by peer".to_string(),
                ))
            }
            Ok(Ok(bytes_read)) => {
                trace!("Read {} bytes from stream", bytes_read);
                self.bytes_read_since_last_processed += bytes_read;
                // Return a slice containing only the *newly* read data plus any previous remainder
                Ok(&self.read_buffer[..self.bytes_read_since_last_processed])
            }
            Ok(Err(e)) => {
                error!("Error reading from stream: {}", e);
                Err(PushClientError::Io(e))
            }
            Err(_) => {
                error!("Timeout reading from stream");
                Err(PushClientError::Timeout("Stream read".to_string()))
            }
        }
    }

    /// Marks a number of bytes as processed, allowing the buffer to be reused.
    pub fn consume_data(&mut self, bytes_processed: usize) {
        if bytes_processed >= self.bytes_read_since_last_processed {
            self.bytes_read_since_last_processed = 0;
        } else {
            // Shift remaining data to the beginning
            self.read_buffer
                .copy_within(bytes_processed..self.bytes_read_since_last_processed, 0);
            self.bytes_read_since_last_processed -= bytes_processed;
            trace!(
                "Consumed {} bytes, {} remaining in buffer",
                bytes_processed, self.bytes_read_since_last_processed
            );
        }
    }

    /// Writes the bytes of an outbound RTMP packet (as a slice) to the stream.
    pub async fn write_packet(&mut self, packet_bytes: &[u8]) -> Result<(), PushClientError> {
        match timeout(WRITE_TIMEOUT, self.stream.write_all(packet_bytes)).await {
            // write_all accepts &[u8]
            Ok(Ok(_)) => {
                Ok(())
            }
            Ok(Err(e)) => {
                Err(PushClientError::Io(e))
            }
            Err(_) => {
                Err(PushClientError::Timeout("Stream write".to_string()))
            }
        }
    }

    pub async fn perform_handshake(&mut self) -> Result<(), PushClientError> {
        let mut handshake = Handshake::new(PeerType::Client); // Corrected constructor
        let p0_and_p1 = handshake.generate_outbound_p0_and_p1().map_err(|e| {
            error!("Failed to generate P0/P1: {}", e);
            PushClientError::HandshakeFailed(format!("P0/P1 generation error: {}", e))
        })?;

        trace!("Sending P0+P1 ({} bytes)", p0_and_p1.len());
        timeout(HANDSHAKE_TIMEOUT, self.stream.write_all(&p0_and_p1))
            .await
            .map_err(|_| PushClientError::Timeout("Handshake P0/P1 write timeout".to_string()))?
            .map_err(|e| {
                error!("Failed to write P0/P1 for handshake: {}", e);
                PushClientError::Io(e)
            })?;
        trace!("P0+P1 sent successfully");

        let mut buffer = vec![0u8; HANDSHAKE_BUFFER_SIZE];

        loop {
            let bytes_read = timeout(HANDSHAKE_TIMEOUT, self.stream.read(&mut buffer))
                .await
                .map_err(|_| PushClientError::Timeout("Handshake read S0/S1/S2".to_string()))? // Timeout error
                .map_err(|e| {
                    error!("Failed to read handshake response: {}", e);
                    PushClientError::Io(e) 
                })?;

            if bytes_read == 0 {
                error!("Connection closed by server during handshake");
                return Err(PushClientError::ConnectionFailed(
                    "Connection closed during handshake".to_string(),
                ));
            }

            trace!("Read {} bytes from server for handshake", bytes_read);
            match handshake.process_bytes(&buffer[..bytes_read]) {
                Ok(HandshakeProcessResult::InProgress { response_bytes }) => {
                    // The library generates response bytes (S0/S1/S2) when needed.
                    // We need to send these bytes back to the peer.
                    if !response_bytes.is_empty() {
                        trace!(
                            "Handshake InProgress: Sending response ({} bytes)",
                            response_bytes.len()
                        );
                        timeout(HANDSHAKE_TIMEOUT, self.stream.write_all(&response_bytes))
                            .await
                            .map_err(|_| {
                                PushClientError::Timeout("Handshake write response".to_string())
                            })?
                            .map_err(|e| {
                                error!("Failed to write handshake response: {}", e);
                                PushClientError::Io(e)
                            })?;
                        trace!("Handshake response sent.");
                    } else {
                        debug!("Handshake InProgress: No response bytes to send yet.");
                    }
                }
                Ok(HandshakeProcessResult::Completed {
                    response_bytes: p2, // This is the final P2 packet from the client perspective
                    remaining_bytes,
                }) => {
                    debug!("Handshake Completed: Received final handshake part.");
                    trace!("Sending P2 ({} bytes)", p2.len());
                    timeout(HANDSHAKE_TIMEOUT, self.stream.write_all(&p2))
                        .await
                        .map_err(|_| PushClientError::Timeout("Handshake write P2".to_string()))? // Timeout error
                        .map_err(|e| {
                            error!("Failed to write P2: {}", e);
                            PushClientError::Io(e) // IO error
                        })?;
                    trace!("P2 sent successfully");

                    if !remaining_bytes.is_empty() {
                        // This case shouldn't typically happen with RTMP handshake if buffer is large enough,
                        // but good to log if it does. These bytes would be for the next phase (RTMP chunks).
                        debug!(
                            "{} remaining bytes after handshake completion",
                            remaining_bytes.len()
                        );
                        // These bytes need to be handled by the ClientSession later.
                        // For now, we assume the connection logic will handle them.
                    }

                    return Ok(());
                }
                Err(e) => {
                    error!("Handshake processing error: {}", e);
                    return Err(PushClientError::HandshakeFailed(format!(
                        "Handshake processing error: {}",
                        e
                    )));
                }
            }
        }
    }

    // Optional: Add a shutdown method if needed
    pub async fn shutdown(&mut self) -> Result<(), PushClientError> {
        info!("Shutting down connection write side");
        self.stream.shutdown().await.map_err(PushClientError::Io)
    }
}
