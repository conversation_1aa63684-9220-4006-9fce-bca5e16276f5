// Re-export key types/functions
pub use crate::rtmp::{
    session::RtmpSession,
    stats::{Stats, StatsSnapshot},
    types::*,
};

use std::sync::Arc;
use tokio::sync::{mpsc, watch};
use tracing::{error, info, warn};

/// High-level RTMP push client with complete lifecycle management
#[derive(Debug)]
pub struct PushClient {
    id: uuid::Uuid,
    rtmp_url: String,
    frame_sender: FrameDataSender,
    frame_receiver: Option<FrameDataReceiver>,
    session_handle: Option<tokio::task::JoinHandle<Result<()>>>,
    state_receiver: watch::Receiver<ClientState>,
    state_sender: watch::Sender<ClientState>,
    stats: Arc<Stats>,
    shutdown_sender: Option<tokio::sync::oneshot::Sender<()>>,
}

impl PushClient {
    /// Create a new RTMP push client
    pub async fn new(rtmp_url: String) -> Self {
        let (frame_sender, frame_receiver) = mpsc::unbounded_channel();
        let (state_sender, state_receiver) = watch::channel(ClientState::Idle);
        let stats = Arc::new(Stats::new());

        Self {
            id: uuid::Uuid::new_v4(),
            rtmp_url,
            frame_sender,
            frame_receiver: Some(frame_receiver),
            session_handle: None,
            state_receiver,
            state_sender,
            stats,
            shutdown_sender: None,
        }
    }

    /// Start the RTMP connection and publishing
    pub async fn start(&mut self) -> Result<()> {
        if self.session_handle.is_some() {
            warn!("RTMP client is already running");
            return Err(PushClientError::InvalidState(
                "Client already started".to_string(),
            ));
        }

        info!("Starting RTMP push for URL: {}", self.rtmp_url);

        let frame_receiver = self.frame_receiver.take().ok_or_else(|| {
            PushClientError::InvalidState("Frame receiver already taken".to_string())
        })?;

        let (shutdown_sender, shutdown_receiver) = tokio::sync::oneshot::channel();
        self.shutdown_sender = Some(shutdown_sender);

        let mut session = RtmpSession::new_with_stats(
            self.rtmp_url.clone(),
            frame_receiver,
            self.stats.clone(),
            self.state_sender.clone(),
        )
        .await;

        // Initialize the session
        session.create().await?;

        // Start the session in a background task
        let session_handle = tokio::spawn(async move {
            tokio::select! {
                result = session.run() => {
                    match result {
                        Ok(_) => {
                            info!("RTMP session completed successfully");
                            Ok(())
                        }
                        Err(e) => {
                            error!("RTMP session error: {:?}", e);
                            Err(e)
                        }
                    }
                }
                _ = shutdown_receiver => {
                    info!("RTMP session shutdown requested");
                    // Attempt graceful shutdown
                    if let Err(e) = session.unpublish().await {
                        warn!("Error during unpublish: {}", e);
                    }
                    Ok(())
                }
            }
        });

        self.session_handle = Some(session_handle);
        info!("RTMP push client started successfully.");
        Ok(())
    }

    /// Stop the RTMP connection
    pub async fn stop(&mut self) -> Result<()> {
        if let Some(shutdown_sender) = self.shutdown_sender.take() {
            let _ = shutdown_sender.send(()); // Ignore if receiver is dropped
        }

        if let Some(handle) = self.session_handle.take() {
            match handle.await {
                Ok(result) => {
                    info!("RTMP session stopped");
                    result
                }
                Err(e) => {
                    error!("Error joining RTMP session task: {}", e);
                    Err(PushClientError::SessionError(format!("Join error: {}", e)))
                }
            }
        } else {
            Ok(())
        }
    }

    /// Send a frame to the RTMP stream
    pub fn send_frame(&self, frame: FrameData) -> Result<()> {
        self.frame_sender.send(frame)?;
        Ok(())
    }

    /// Get the current state of the client
    pub async fn get_state(&self) -> ClientState {
        self.state_receiver.borrow().clone()
    }

    /// Wait for a specific state change
    pub async fn wait_for_state(&mut self, target_state: ClientState) -> Result<()> {
        while *self.state_receiver.borrow() != target_state {
            if let Err(_) = self.state_receiver.changed().await {
                return Err(PushClientError::SessionError(
                    "State channel closed".to_string(),
                ));
            }
        }
        Ok(())
    }

    /// Check if the client is currently publishing
    pub fn is_publishing(&self) -> bool {
        *self.state_receiver.borrow() == ClientState::Publishing
    }

    /// Check if the client is connected
    pub fn is_connected(&self) -> bool {
        matches!(
            *self.state_receiver.borrow(),
            ClientState::Connected
                | ClientState::SendingConnect
                | ClientState::SendingPublish
                | ClientState::Publishing
        )
    }

    /// Get current statistics
    pub fn get_stats(&self) -> StatsSnapshot {
        self.stats.snapshot()
    }

    /// Reset statistics
    pub fn reset_stats(&self) {
        self.stats.reset();
    }

    /// Get the frame sender for external use
    pub fn frame_sender(&self) -> FrameDataSender {
        self.frame_sender.clone()
    }

    pub fn id(&self) -> uuid::Uuid {
        self.id
    }
}

impl Drop for PushClient {
    fn drop(&mut self) {
        // Send shutdown signal if still running
        if let Some(shutdown_sender) = self.shutdown_sender.take() {
            let _ = shutdown_sender.send(());
        }
    }
}

/// Builder for creating PushClient with custom configuration
#[derive(Default)]
pub struct PushClientBuilder {
    rtmp_url: Option<String>,
}

impl PushClientBuilder {
    pub fn new() -> Self {
        Self::default()
    }

    pub fn url<S: Into<String>>(mut self, url: S) -> Self {
        self.rtmp_url = Some(url.into());
        self
    }

    pub async fn build(self) -> Result<PushClient> {
        let rtmp_url = self
            .rtmp_url
            .ok_or_else(|| PushClientError::InvalidState("RTMP URL is required".to_string()))?;

        Ok(PushClient::new(rtmp_url).await)
    }
}

/// Convenience function to create a new PushClient
pub async fn new_push_client(rtmp_url: String) -> PushClient {
    PushClient::new(rtmp_url).await
}

/// Convenience function to create a PushClient builder
pub fn push_client() -> PushClientBuilder {
    PushClientBuilder::new()
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::rtmp::types::FrameData;
    use bytes::Bytes;

    #[tokio::test]
    async fn test_push_client_creation() {
        let client = PushClient::new("rtmp://localhost/live/test".to_string()).await;
        assert_eq!(client.rtmp_url, "rtmp://localhost/live/test");
        assert_eq!(client.get_state().await, ClientState::Idle);
    }

    #[tokio::test]
    async fn test_push_client_builder() {
        let client = push_client()
            .url("rtmp://localhost/live/test")
            .build()
            .await
            .expect("Failed to build client");

        assert_eq!(client.rtmp_url, "rtmp://localhost/live/test");
    }

    #[tokio::test]
    async fn test_stats_tracking() {
        let client = PushClient::new("rtmp://localhost/live/test".to_string()).await;
        let stats = client.get_stats();

        // Initial stats should be zero
        assert_eq!(stats.bytes_sent, 0);
        assert_eq!(stats.frames_sent, 0);
        assert_eq!(stats.connection_errors, 0);
    }

    #[test]
    fn test_frame_data_methods() {
        let video_frame = FrameData::Video {
            timestamp: 1000,
            payload: Bytes::from(vec![1, 2, 3, 4]),
        };

        assert_eq!(video_frame.len(), 4);
        assert_eq!(video_frame.timestamp_ms(), 1000);

        let audio_frame = FrameData::Audio {
            timestamp: 2000,
            payload: Bytes::from(vec![5, 6]),
        };

        assert_eq!(audio_frame.len(), 2);
        assert_eq!(audio_frame.timestamp_ms(), 2000);
    }
}

/// Example usage of the RTMP push client
///
/// ```no_run
/// use rtmp::{PushClient, FrameData};
/// use bytes::Bytes;
///
/// #[tokio::main]
/// async fn main() -> Result<(), Box<dyn std::error::Error>> {
///     // Create a new push client
///     let mut client = PushClient::new("rtmp://localhost/live/stream".to_string()).await;
///
///     // Start the RTMP connection
///     client.start().await?;
///
///     // Wait for publishing state
///     client.wait_for_state(rtmp::ClientState::Publishing).await?;
///
///     // Send some video frames
///     for i in 0..100 {
///         let frame = FrameData::Video {
///             timestamp: i * 33, // ~30 FPS
///             data: Bytes::from(vec![0u8; 1024]), // Dummy data
///             is_keyframe: i % 30 == 0, // Keyframe every second
///         };
///
///         client.send_frame(frame).await?;
///         tokio::time::sleep(tokio::time::Duration::from_millis(33)).await;
///     }
///
///     // Get statistics
///     let stats = client.get_stats();
///     println!("Sent {} frames, {} bytes", stats.frames_sent, stats.bytes_sent);
///
///     // Stop the client
///     client.stop().await?;
///
///     Ok(())
/// }
/// ```
pub mod examples {}
