[package]
name = "stream_client"
version = "0.1.0"
edition = "2024"

[dependencies]
anyhow = { version = "1.0.98", features = ["backtrace"] }
hang = "0.2.0"
moq-lite = {version = "0.2.0", features = ["serde"] }

slab = "0.4.2"
bytes = "1"
rml_rtmp = "0.8.0"
tokio = { version = "1.44.2", features = ["rt", "macros", "net", "io-util"] }
clap = { version = "4.5.37", features = ["derive"] }
rand = "0.9.1"
url = "2.5.2"
thiserror = "2.0.12"
tracing = "0.1"
serde = { version = "1.0", features = ["derive"] }

xflv = "0.4.4"
bytesio = "0.3.4"
async-trait = "0.1.62"
indexmap = "1.9.3"
uuid = { version = "1.0", features = ["v4"] }
