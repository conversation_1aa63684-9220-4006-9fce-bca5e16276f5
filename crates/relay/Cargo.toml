[package]
name = "relay"
version = "0.1.0"
edition = "2024"

[dependencies]
anyhow = { version = "1.0.98", features = ["backtrace"] }
moq-native = { version = "0.6" }
hang = "0.2.0"
moq-lite = {version = "0.2.0", features = ["serde"] }

url = "2"
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0.140"
uuid = { version = "1.17.0", features = ["serde", "v4"] }

tokio = { version = "1.44.2", features = ["full"] }
tracing = "0.1.41"

stream_client = { path = "../../crates/stream_client" }
streamhub = { path = "../../crates/streamhub" }
config = { path = "../../crates/config" }