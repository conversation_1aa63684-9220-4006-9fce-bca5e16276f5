use std::collections::HashMap;

use anyhow::Context;
use moq_lite::{Announce, BroadcastConsumer, BroadcastProducer, Origin};
use moq_native::quic;
use tracing::Instrument;
use url::Url;

use config::ClusterConfig; // Import ClusterConfig from its new location

#[derive(Clone)]
pub struct Cluster {
    config: ClusterConfig, // This will now use the imported ClusterConfig
    client: quic::Client,

    // Tracks announced by local clients (users).
    pub locals: Origin,

    // Tracks announced by remote servers (cluster).
    pub remotes: Origin,
}

impl Cluster {
    const ORIGINS: &str = "internal/origins/";

    pub fn new(config: ClusterConfig, client: quic::Client) -> Self {
        Cluster {
            config,
            client,
            locals: Origin::new(),
            remotes: Origin::new(),
        }
    }

    pub fn consume(&self, broadcast: &str) -> Option<BroadcastConsumer> {
        self.locals
            .consume(broadcast)
            .or_else(|| self.remotes.consume(broadcast))
    }

    pub async fn connect(&self, url: Url) -> Result<moq_lite::Session, anyhow::Error> {
        let session = self.client.connect(url).await?;
        Ok(moq_lite::Session::connect(session).await?)
    }

    pub async fn run(self) -> anyhow::Result<()> {
        let root = self.config.cluster_root.clone();
        let node = self
            .config
            .cluster_node
            .as_ref()
            .map(|node| node.to_string());

        tracing::info!(?root, ?node, "initializing cluster");

        // Create a "broadcast" with no tracks to announce ourselves.
        let noop = BroadcastProducer::new();

        // If we're a node, then we need to announce ourselves as an origin.
        // We do this by creating a "broadcast" with no tracks.
        let myself = self
            .config
            .cluster_node
            .as_ref()
            .map(|node| format!("internal/origins/{}", node));

        // If we're using a root node, then we have to connect to it.
        let mut announced = match root.as_ref() {
            Some(root) if Some(root) != node.as_ref() => {
                tracing::info!(?root, "connecting to root");

                // Connect to the root node.
                let url = Url::parse(&format!("https://{}", root)).context("invalid root URL")?;
                let mut root = self.connect(url).await?;

                // Announce ourselves as an origin to the root node.
                if let Some(myself) = &myself {
                    root.publish(myself, noop.consume());
                }

                // Subscribe to available origins.
                root.announced(Self::ORIGINS)
            }
            // Otherwise, we're the root node but we still want to connect to other nodes.
            _ => {
                // Announce ourselves as an origin to all connected clients.
                // Technically, we should only announce to cluster clients (not end users), but who cares.
                let mut locals = self.locals.clone();
                if let Some(myself) = &myself {
                    locals.publish(myself, noop.consume());
                }

                // Subscribe to the available origins.
                self.locals.announced(Self::ORIGINS)
            }
        };

        // Keep track of the active remotes.
        let mut remotes = HashMap::new();

        // Discover other origins.
        // NOTE: The root node will connect to all other nodes as a client, ignoring the existing (server) connection.
        // This ensures that nodes are advertising a valid hostname before any tracks get announced.
        while let Some(announce) = announced.next().await {
            match announce {
                Announce::Active { suffix: host } => {
                    if Some(&host) == node.as_ref() {
                        // Skip ourselves.
                        continue;
                    }

                    tracing::info!(?host, "discovered origin");

                    let mut this = self.clone();
                    let remote = host.clone();

                    let handle = tokio::spawn(
                        async move {
                            loop {
                                if let Err(err) = this.run_remote(&remote).await {
                                    tracing::error!(?err, "remote error, retrying");
                                }

                                // TODO smarter backoff
                                tokio::time::sleep(tokio::time::Duration::from_secs(1)).await;
                            }
                        }
                        .in_current_span(),
                    );

                    remotes.insert(host, handle);
                }
                Announce::Ended { suffix: host } => {
                    if let Some(handle) = remotes.remove(&host) {
                        tracing::warn!(?host, "terminating remote");
                        handle.abort();
                    }
                }
            }
        }
        Ok(())
    }

    #[tracing::instrument("remote", skip_all, err, fields(%host))]
    async fn run_remote(&mut self, host: &str) -> anyhow::Result<()> {
        let url = Url::parse(&format!("https://{}", host)).context("invalid node URL")?;
        let session = self.connect(url).await?;

        let mut session1 = session.clone();
        let mut session2 = session.clone();

        tokio::select! {
            // NOTE: We only announce local tracks to remote nodes.
            // Otherwise there would be conflicts and we wouldn't know which node is the origin.
            _ = session1.consume_from(self.locals.clone(), "") => Ok(()),
            // We take any of their remote broadcasts and announce them ourselves.
            _ = session2.publish_to(self.remotes.clone(), "") => Ok(()),
            err = session.closed() => Err(err.into()),
        }
    }
}
