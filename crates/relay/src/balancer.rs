use std::collections::HashMap;
use std::sync::Arc;
use std::time::Duration;

use moq_lite::*;
use serde::{Serialize, Deserialize};
use tokio::sync::RwLock;
use tracing;

use config::LoadBalancerConfig;

// Node status information
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct NodeStatus {
    pub node_id: String,
    pub active_streams: usize,
    pub max_streams: usize,
    pub available: bool,
    pub cpu_usage: f32,
    pub memory_usage: f32,
    pub bandwidth_usage: f32,
}

// Shared state for node statuses
pub type NodeStatusMap = Arc<RwLock<HashMap<String, NodeStatus>>>;

pub struct LoadBalancer {
    pub config: LoadBalancerConfig,
    pub node_statuses: NodeStatusMap,
    last_broadcasted_status: Arc<RwLock<Option<NodeStatus>>>,
    status_change_threshold: f32,
}

impl LoadBalancer {
    pub const STATUS_PATH: &'static str = "internal/status/";
    
    pub fn new(config: LoadBalancerConfig) -> Self {
        // Initialize node statuses map
        let node_statuses = Arc::new(RwLock::new(HashMap::new()));
        
        // Initialize our own status if we're a node
        if let Some(node_id) = &config.node_id {
            let initial_status = NodeStatus {
                node_id: node_id.clone(),
                active_streams: 0,
                max_streams: config.max_streams,
                available: true,
                cpu_usage: 0.0,
                memory_usage: 0.0,
                bandwidth_usage: 0.0,
            };
            
            let node_statuses_clone = node_statuses.clone();
            let node_id_clone = node_id.clone();
            
            tokio::spawn(async move {
                let mut statuses = node_statuses_clone.write().await;
                statuses.insert(node_id_clone, initial_status);
            });
        }
        
        // Initialize last broadcasted status
        let last_broadcasted_status = Arc::new(RwLock::new(None));
        
        LoadBalancer {
            config,
            node_statuses,
            last_broadcasted_status,
            status_change_threshold: 0.1, // 10% change threshold
        }
    }
    
    pub async fn run(&self, locals: Origin, remotes: Origin) -> anyhow::Result<()> {
        if !self.config.enable_load_balancing {
            return Ok(());
        }
        
        // Start status broadcasting if we're a node
        if self.config.node_id.is_some() {
            let this = self.clone();
            let locals = locals.clone();
            tokio::spawn(async move {
                this.start_status_broadcasting(locals).await;
            });
        }
        
        // Start status subscription
        let this = self.clone();
        let remotes = remotes.clone();
        tokio::spawn(async move {
            this.start_status_subscription(remotes).await;
        });
        
        Ok(())
    }
    
    /// Broadcast our node status to the cluster
    async fn start_status_broadcasting(&self, mut locals: Origin) {
        // Only broadcast if we're part of a cluster
        if let Some(node_id) = &self.config.node_id {
            // Create a broadcast producer for our status
            let status_broadcast = format!("{}{}", Self::STATUS_PATH, node_id);
            let noop = BroadcastProducer::new();
            
            // Publish the broadcast
            locals.publish(&status_broadcast, noop.consume());
            
            // Update and broadcast status periodically
            let mut interval = tokio::time::interval(Duration::from_secs(1));
            let node_statuses = self.node_statuses.clone();
            let last_broadcasted = self.last_broadcasted_status.clone();
            let threshold = self.status_change_threshold;
            
            loop {
                interval.tick().await;
                
                // Update our status with current metrics
                let mut status = {
                    let statuses = node_statuses.read().await;
                    statuses.get(node_id).cloned().unwrap_or_else(|| NodeStatus {
                        node_id: node_id.clone(),
                        active_streams: 0,
                        max_streams: self.config.max_streams,
                        available: true,
                        cpu_usage: 0.0,
                        memory_usage: 0.0,
                        bandwidth_usage: 0.0,
                    })
                };
                
                // Update metrics (in a real implementation, these would be actual metrics)
                status.active_streams = self.count_active_streams();
                status.available = status.active_streams < status.max_streams / 2;
                
                // In a real implementation, you would collect these metrics from the system
                status.cpu_usage = self.get_cpu_usage();
                status.memory_usage = self.get_memory_usage();
                status.bandwidth_usage = self.get_bandwidth_usage();
                
                // Update our stored status
                {
                    let mut statuses = node_statuses.write().await;
                    statuses.insert(node_id.clone(), status.clone());
                }
                
                // Check if we need to broadcast the status
                let should_broadcast = {
                    let last = last_broadcasted.read().await;
                    match &*last {
                        None => true, // Always broadcast the first status
                        Some(last_status) => {
                            // Check if availability changed
                            if last_status.available != status.available {
                                true
                            } else {
                                // Check if any metric changed significantly
                                let cpu_change = (last_status.cpu_usage - status.cpu_usage).abs() / 100.0;
                                let memory_change = (last_status.memory_usage - status.memory_usage).abs() / 100.0;
                                let bandwidth_change = (last_status.bandwidth_usage - status.bandwidth_usage).abs() / 100.0;
                                let streams_change = if last_status.active_streams == 0 && status.active_streams > 0 {
                                    1.0 // If we went from 0 to any streams, that's a 100% change
                                } else if last_status.active_streams > 0 {
                                    (last_status.active_streams as f32 - status.active_streams as f32).abs() / last_status.active_streams as f32
                                } else {
                                    0.0
                                };
                                
                                // Broadcast if any metric changed more than the threshold
                                cpu_change > threshold || 
                                memory_change > threshold || 
                                bandwidth_change > threshold || 
                                streams_change > threshold
                            }
                        }
                    }
                };
                
                // Broadcast our status if needed
                if should_broadcast {
                    tracing::debug!(node_id = %node_id, "Broadcasting status update");
                    
                    // Update last broadcasted status
                    {
                        let mut last = last_broadcasted.write().await;
                        *last = Some(status.clone());
                    }
                    
                    // Broadcast our status
                    let status_json = serde_json::to_string(&status).unwrap_or_default();
                    let track = moq_lite::Track {
                        name: "status".to_string(),
                        priority: 0,
                    };

                    let mut status_producer = noop.create(track);
                    let sequence = 0 as u32;
                    
                    if let Some(mut group) = status_producer.create_group(sequence.into()) {
                       group.write_frame(status_json.into_bytes());
                    }
                }
            }
        }
    }
    
    /// Subscribe to status broadcasts from other nodes
    async fn start_status_subscription(&self, remotes: Origin) {
        // Subscribe to all status broadcasts
        let mut status_announcements = remotes.announced(Self::STATUS_PATH);
        let node_statuses = self.node_statuses.clone();
        
        while let Some(announce) = status_announcements.next().await {
            if let moq_lite::Announce::Active { suffix } = announce {
                let status_path = format!("{}{}", Self::STATUS_PATH, suffix);
                
                // Subscribe to this node's status broadcast
                if let Some(broadcast) = remotes.consume(&status_path) {
                    let track = moq_lite::Track {
                        name: "status".to_string(),
                        priority: 0,
                    };
                    
                    let mut track_consumer = broadcast.subscribe(&track);
                    let node_id = suffix.clone();
                    let node_statuses_clone = node_statuses.clone();
                    
                    // Process status updates from this node
                    tokio::spawn(async move {
                        while let Ok(Some(mut group)) = track_consumer.next_group().await {
                            if let Ok(Some(mut frame)) = group.next_frame().await {
                                if let Ok(data) = frame.read_all().await {
                                    if let Ok(status) = serde_json::from_slice::<NodeStatus>(&data) {
                                        // Update our map of node statuses
                                        let mut statuses = node_statuses_clone.write().await;
                                        statuses.insert(node_id.clone(), status);
                                    }
                                }
                            }
                        }
                    });
                }
            }
        }
    }
    
    /// Count the number of active streams on this node
    fn count_active_streams(&self) -> usize {
        // In a real implementation, you would count actual active streams
        // This could integrate with the hub to get real stream counts
        // For now, we'll just return a placeholder value
        // TODO: Implement actual stream counting via hub integration
        10
    }
    
    /// Update the active streams count for this node
    pub async fn update_active_streams(&self, count: usize) {
        if let Some(node_id) = &self.config.node_id {
            let mut statuses = self.node_statuses.write().await;
            if let Some(status) = statuses.get_mut(node_id) {
                status.active_streams = count;
                status.available = count < status.max_streams / 2;
            }
        }
    }
    
    /// Get all node statuses
    pub async fn get_node_statuses(&self) -> Vec<NodeStatus> {
        let statuses = self.node_statuses.read().await;
        statuses.values().cloned().collect()
    }
    
    /// Find the recommended node for the required number of streams
    pub async fn find_recommended_node(&self, required_streams: usize) -> Option<NodeStatus> {
        // Get all node statuses
        let all_statuses = self.get_node_statuses().await;
        
        // Filter nodes that meet the stream requirement
        // This is where we perform the final check for the specific client stream requirement
        let available_nodes: Vec<NodeStatus> = all_statuses.into_iter()
            .filter(|status| {
                // Check if the node is generally available
                status.available && 
                // Perform the final check for the specific stream requirement
                (status.max_streams - status.active_streams) >= required_streams
            })
            .collect();
        
        // Find the best node using our multi-factor algorithm
        available_nodes.into_iter()
            .min_by(|a, b| {
                // First compare by active_streams
                let streams_cmp = a.active_streams.cmp(&b.active_streams);
                if streams_cmp != std::cmp::Ordering::Equal {
                    return streams_cmp;
                }
                
                // If equal, compare by CPU usage
                let cpu_cmp = a.cpu_usage.partial_cmp(&b.cpu_usage).unwrap_or(std::cmp::Ordering::Equal);
                if cpu_cmp != std::cmp::Ordering::Equal {
                    return cpu_cmp;
                }
                
                // If still equal, compare by bandwidth usage
                a.bandwidth_usage.partial_cmp(&b.bandwidth_usage).unwrap_or(std::cmp::Ordering::Equal)
            })
    }
    
    /// Find available nodes that can handle the required number of streams
    pub async fn find_available_nodes(&self, required_streams: usize) -> Vec<NodeStatus> {
        // Get all node statuses
        let all_statuses = self.get_node_statuses().await;
        
        // Filter nodes that meet the stream requirement
        all_statuses.into_iter()
            .filter(|status| {
                // Check if the node is generally available
                status.available && 
                // Perform the final check for the specific stream requirement
                (status.max_streams - status.active_streams) >= required_streams
            })
            .collect()
    }
    
    // Helper methods to get system metrics
    fn get_cpu_usage(&self) -> f32 {
        // In a real implementation, you would get the actual CPU usage
        // For now, we'll just use a placeholder value
        50.0 // Example value
    }
    
    fn get_memory_usage(&self) -> f32 {
        // In a real implementation, you would get the actual memory usage
        // For now, we'll just use a placeholder value
        40.0 // Example value
    }
    
    fn get_bandwidth_usage(&self) -> f32 {
        // In a real implementation, you would get the actual bandwidth usage
        // For now, we'll just use a placeholder value
        30.0 // Example value
    }
}

impl Clone for LoadBalancer {
    fn clone(&self) -> Self {
        LoadBalancer {
            config: LoadBalancerConfig {
                node_id: self.config.node_id.clone(),
                max_streams: self.config.max_streams,
                enable_load_balancing: self.config.enable_load_balancing,
            },
            node_statuses: self.node_statuses.clone(),
            last_broadcasted_status: self.last_broadcasted_status.clone(),
            status_change_threshold: self.status_change_threshold,
        }
    }
}