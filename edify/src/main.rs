mod rpc;

use config::Config;
use relay::{Cluster, Connection, LoadBalancer};
use rpc::create_full_server;

use anyhow::Context;
use clap::Parser;
use moq_native::quic;
use std::sync::Arc;
use streamhub::StreamHub;

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    let config = Config::parse();
    config.log.init();

    let bind = tokio::net::lookup_host(config.relay.bind)
        .await
        .context("invalid bind address")?
        .next()
        .context("invalid bind address")?;

    let tls = config.relay.tls.load()?;
    if tls.server.is_none() {
        anyhow::bail!("missing TLS certificates");
    }

    let quic = quic::Endpoint::new(quic::Config {
        bind,
        tls: tls.clone(),
    })?;
    let mut server = quic.server.context("missing TLS certificate")?;
    let cluster = Cluster::new(config.relay.cluster.clone(), quic.client);
    let cloned = cluster.clone();
    tokio::spawn(async move { cloned.run().await.expect("cluster failed") });

    // Create load balancer
    let load_balancer = LoadBalancer::new(config.load_balancer.clone());
    load_balancer
        .run(cluster.locals.clone(), cluster.remotes.clone())
        .await?;
    let load_balancer = Arc::new(load_balancer);

    // Initialize the stream hub if enabled
    let hub_handle = if config.hub.enable_hub {
        tracing::info!("Initializing stream hub...");

        // Create the core distribution hub
        let mut stream_hub  = StreamHub::new();
        let handle = stream_hub.handle();
        let hub_task = tokio::spawn(async move {
            if let Err(e) = stream_hub.run().await {
                tracing::error!("Stream hub error: {:?}", e);
            }
        });
        tracing::info!("Stream hub initialized successfully");
        Some((handle, hub_task))
    } else {
        tracing::info!("Stream hub disabled");
        None
    };

    // Create RPC server with proper state management including hub
    let rpc_bind_addr = std::net::SocketAddr::new(bind.ip(), 8080); // Use different port for RPC
    let rpc_server = create_full_server(
        rpc_bind_addr,
        hub_handle.as_ref().map(|(handle, _)| handle.clone()),
        Some(load_balancer.clone()),
        Some(Arc::new(cluster.clone())),
    )?;

    tokio::spawn(async move {
        rpc_server.run().await.expect("failed to run RPC server");
    });

    tracing::info!(addr = %bind, "listening");

    let mut conn_id = 0;

    while let Some(conn) = server.accept().await {
        let session = Connection::new(conn_id, conn.into(), cluster.clone());
        conn_id += 1;
        tokio::spawn(session.run());
    }

    Ok(())
}
