use axum::{
    Json, Router,
    extract::{Query, State},
    http::StatusCode,
    response::IntoResponse,
    routing::{get, post},
};
use relay::NodeStatus;
use serde::Deserialize;

use super::{AppState, types::*};

/// Create load balancer routes
pub fn create_routes() -> Router<AppState> {
    Router::new()
        .route("/status", get(get_balancer_status))
        .route("/nodes", get(list_nodes))
        .route("/recommend", post(get_recommendation))
        .route("/recommend", get(get_recommendation_simple))
}

#[derive(Deserialize)]
struct RecommendationQuery {
    stream_type: Option<String>,
    region: Option<String>,
}

// =============================================================================
// LOAD BALANCER STATUS AND NODES
// =============================================================================

/// Get load balancer status
async fn get_balancer_status(State(state): State<AppState>) -> impl IntoResponse {
    match &state.load_balancer {
        Some(_balancer) => {
            // TODO: Get real status from load balancer
            (
                StatusCode::OK,
                Json(RpcResponse::success(serde_json::json!({
                    "status": "active",
                    "node_count": 0, // TODO: Get real count
                    "total_load": 0.0,
                    "last_update": chrono::Utc::now().timestamp()
                }))),
            )
        }
        None => {
            let error = ErrorResponse {
                code: error_codes::HUB_UNAVAILABLE.to_string(),
                message: "Load balancer is not available".to_string(),
                details: None,
            };
            (
                StatusCode::SERVICE_UNAVAILABLE,
                Json(RpcResponse::<serde_json::Value>::error(error.message)),
            )
        }
    }
}

/// List all available nodes
async fn list_nodes(State(state): State<AppState>) -> impl IntoResponse {
    match &state.load_balancer {
        Some(balancer) => {
            // Get real nodes from load balancer
            let node_statuses = balancer.get_node_statuses().await;
            let nodes: Vec<NodeStatusResponse> =
                node_statuses.iter().map(convert_node_status).collect();
            (StatusCode::OK, Json(RpcResponse::success(nodes)))
        }
        None => {
            let error = ErrorResponse {
                code: error_codes::HUB_UNAVAILABLE.to_string(),
                message: "Load balancer is not available".to_string(),
                details: None,
            };
            (
                StatusCode::SERVICE_UNAVAILABLE,
                Json(RpcResponse::<Vec<NodeStatusResponse>>::error(error.message)),
            )
        }
    }
}

// =============================================================================
// NODE RECOMMENDATION
// =============================================================================

/// Get node recommendation (POST with JSON body)
async fn get_recommendation(
    State(state): State<AppState>,
    Json(request): Json<RecommendationRequest>,
) -> impl IntoResponse {
    match &state.load_balancer {
        Some(balancer) => {
            // Use actual recommendation logic from load balancer
            // Parse requirements from the request or use default
            let required_streams = 1; // Default value
            
            // Use stream type and region preferences from request for logging
            let stream_type = request.stream_type.as_deref().unwrap_or("unknown");
            let region = request.preferred_region.as_deref().unwrap_or("any");

            // Try to find a recommended node
            if let Some(recommended_node) = balancer.find_recommended_node(required_streams).await {
                // Get alternative nodes
                let alternative_nodes = balancer
                    .find_available_nodes(required_streams)
                    .await
                    .into_iter()
                    .filter(|node| node.node_id != recommended_node.node_id)
                    .take(3) // Limit to 3 alternatives
                    .map(|node| convert_node_status(&node))
                    .collect();

                let recommendation = RecommendationResponse {
                    recommended_node: convert_node_status(&recommended_node),
                    alternative_nodes,
                    reason: format!(
                        "Recommended {} node for stream type '{}' in region '{}' with {} required streams",
                        &recommended_node.node_id, stream_type, region, required_streams
                    ),
                };

                (StatusCode::OK, Json(RpcResponse::success(recommendation)))
            } else {
                let error = ErrorResponse {
                    code: error_codes::NOT_FOUND.to_string(),
                    message: "No suitable nodes available for the requested requirements"
                        .to_string(),
                    details: None,
                };
                (
                    StatusCode::NOT_FOUND,
                    Json(RpcResponse::<RecommendationResponse>::error(error.message)),
                )
            }
        }
        None => {
            let error = ErrorResponse {
                code: error_codes::HUB_UNAVAILABLE.to_string(),
                message: "Load balancer is not available".to_string(),
                details: None,
            };
            (
                StatusCode::SERVICE_UNAVAILABLE,
                Json(RpcResponse::<RecommendationResponse>::error(error.message)),
            )
        }
    }
}

/// Get node recommendation (GET with query parameters)
async fn get_recommendation_simple(
    State(state): State<AppState>,
    Query(params): Query<RecommendationQuery>,
) -> impl IntoResponse {
    // Convert query params to request struct
    let request = RecommendationRequest {
        stream_type: params.stream_type,
        preferred_region: params.region,
        requirements: None,
    };

    // Use the same logic as the POST handler
    match &state.load_balancer {
        Some(balancer) => {
            let required_streams = 1; // Default for simple recommendation

            // Try to find a recommended node
            if let Some(recommended_node) = balancer.find_recommended_node(required_streams).await {
                // Get alternative nodes
                let alternative_nodes = balancer
                    .find_available_nodes(required_streams)
                    .await
                    .into_iter()
                    .filter(|node| node.node_id != recommended_node.node_id)
                    .take(2) // Fewer alternatives for simple query
                    .map(|node| convert_node_status(&node))
                    .collect();

                let recommendation = RecommendationResponse {
                    recommended_node: convert_node_status(&recommended_node),
                    alternative_nodes,
                    reason: format!(
                        "Simple recommendation for stream type: {:?}",
                        request.stream_type
                    ),
                };

                (StatusCode::OK, Json(RpcResponse::success(recommendation)))
            } else {
                let error = ErrorResponse {
                    code: error_codes::NOT_FOUND.to_string(),
                    message: "No suitable nodes available".to_string(),
                    details: None,
                };
                (
                    StatusCode::NOT_FOUND,
                    Json(RpcResponse::<RecommendationResponse>::error(error.message)),
                )
            }
        }
        None => {
            let error = ErrorResponse {
                code: error_codes::HUB_UNAVAILABLE.to_string(),
                message: "Load balancer is not available".to_string(),
                details: None,
            };
            (
                StatusCode::SERVICE_UNAVAILABLE,
                Json(RpcResponse::<RecommendationResponse>::error(error.message)),
            )
        }
    }
}

// =============================================================================
// HELPER FUNCTIONS
// =============================================================================

fn convert_node_status(status: &NodeStatus) -> NodeStatusResponse {
    NodeStatusResponse {
        node_id: status.node_id.clone(),
        address: "unknown".to_string(), // TODO: Get real address when available
        load: status.cpu_usage,         // Use CPU usage as a proxy for load
        active_streams: status.active_streams,
        last_heartbeat: chrono::Utc::now().timestamp(), // TODO: Add real timestamp
        is_available: status.available,
    }
}
