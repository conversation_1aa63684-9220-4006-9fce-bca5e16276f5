use axum::{
    Router,
    extract::{State, Path},
    routing::get,
    response::IntoResponse,
    http::StatusCode,
    body::Body,
};
use bytes::Bytes;
use futures::FutureExt;

use super::AppState;

/// Create MoQ relay routes (from the original web.rs)
pub fn create_routes() -> Router<AppState> {
    Router::new()
        .route("/certificate.sha256", get(certificate_fingerprint))
        .route("/announced", get(serve_announced_root))
        .route("/announced/*prefix", get(serve_announced))
        .route("/fetch/*path", get(serve_fetch))
}

/// Get TLS certificate fingerprint - returns the first certificate fingerprint
async fn certificate_fingerprint(State(state): State<AppState>) -> impl IntoResponse {
    match &state.tls_fingerprint {
        Some(fingerprint) => {
            // Return the actual TLS fingerprint as plain text (matching original behavior)
            fingerprint.clone()
        }
        None => {
            // Return a placeholder fingerprint when not available
            "certificate-fingerprint-not-configured".to_string()
        }
    }
}

/// Serve announced tracks for root path (empty prefix)
async fn serve_announced_root(State(state): State<AppState>) -> impl IntoResponse {
    serve_announced_impl(state, "".to_string()).await
}

/// Serve announced tracks for a given prefix
async fn serve_announced(State(state): State<AppState>, Path(prefix): Path<String>) -> impl IntoResponse {
    serve_announced_impl(state, prefix).await
}

/// Implementation of serve_announced functionality from original web.rs
async fn serve_announced_impl(state: AppState, prefix: String) -> impl IntoResponse {
    match &state.cluster {
        Some(cluster) => {
            // Following the original implementation from web.rs lines 115-133
            let mut local = cluster.locals.announced(&prefix);
            let mut remote = cluster.remotes.announced(&prefix);

            let mut broadcasts = Vec::new();

            // Get local announcements
            while let Some(Some(local)) = local.next().now_or_never() {
                if let moq_lite::Announce::Active { suffix } = local {
                    broadcasts.push(suffix);
                }
            }

            // Get remote announcements
            while let Some(Some(remote)) = remote.next().now_or_never() {
                if let moq_lite::Announce::Active { suffix } = remote {
                    broadcasts.push(suffix);
                }
            }

            // Return as newline-separated list (matching original behavior)
            (StatusCode::OK, broadcasts.join("\n"))
        }
        None => {
            (StatusCode::SERVICE_UNAVAILABLE, "Cluster not available".to_string())
        }
    }
}

/// Serve the latest group for a given track (from original web.rs lines 136-163)
async fn serve_fetch(State(state): State<AppState>, Path(path): Path<String>) -> axum::response::Result<ServeGroup> {
    match &state.cluster {
        Some(cluster) => {
            let mut path_parts: Vec<&str> = path.split("/").collect();
            if path_parts.len() < 2 {
                return Err(StatusCode::BAD_REQUEST.into());
            }

            let track_name = path_parts.pop().unwrap().to_string();
            let broadcast = path_parts.join("/");

            let track = moq_lite::Track {
                name: track_name,
                priority: 0,
            };

            tracing::info!(?broadcast, ?track, "subscribing to track");

            let broadcast = cluster.consume(&broadcast).ok_or(StatusCode::NOT_FOUND)?;
            let mut track = broadcast.subscribe(&track);

            let group = match track.next_group().await {
                Ok(Some(group)) => group,
                Ok(None) => return Err(StatusCode::NOT_FOUND.into()),
                Err(_) => return Err(StatusCode::INTERNAL_SERVER_ERROR.into()),
            };

            Ok(ServeGroup::new(group))
        }
        None => {
            Err(StatusCode::SERVICE_UNAVAILABLE.into())
        }
    }
}

// =============================================================================
// SERVE GROUP IMPLEMENTATION (from original web.rs lines 198-263)
// =============================================================================

struct ServeGroup {
    group: moq_lite::GroupConsumer,
    frame: Option<moq_lite::FrameConsumer>,
}

impl ServeGroup {
    fn new(group: moq_lite::GroupConsumer) -> Self {
        Self { group, frame: None }
    }

    async fn next(&mut self) -> moq_lite::Result<Option<Bytes>> {
        loop {
            if let Some(frame) = self.frame.as_mut() {
                let data = frame.read_all().await?;
                self.frame.take();
                return Ok(Some(data));
            }

            self.frame = self.group.next_frame().await?;
            if self.frame.is_none() {
                return Ok(None);
            }
        }
    }
}

impl IntoResponse for ServeGroup {
    fn into_response(self) -> axum::response::Response {
        axum::response::Response::new(Body::new(self))
    }
}

impl http_body::Body for ServeGroup {
    type Data = Bytes;
    type Error = ServeGroupError;

    fn poll_frame(
        self: std::pin::Pin<&mut Self>,
        cx: &mut std::task::Context<'_>,
    ) -> std::task::Poll<Option<Result<http_body::Frame<Self::Data>, Self::Error>>> {
        use std::task::{ready, Poll};
        let this = self.get_mut();

        // Use `poll_fn` to turn the async function into a Future
        let future = this.next();
        tokio::pin!(future);

        match ready!(future.poll(cx)) {
            Ok(Some(data)) => {
                let frame = http_body::Frame::data(data);
                Poll::Ready(Some(Ok(frame)))
            }
            Ok(None) => Poll::Ready(None),
            Err(e) => Poll::Ready(Some(Err(ServeGroupError(e)))),
        }
    }
}

#[derive(Debug, thiserror::Error)]
#[error(transparent)]
struct ServeGroupError(moq_lite::Error);

impl IntoResponse for ServeGroupError {
    fn into_response(self) -> axum::response::Response {
        (StatusCode::INTERNAL_SERVER_ERROR, self.0.to_string()).into_response()
    }
}
