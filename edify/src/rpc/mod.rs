use axum::{Router, extract::State};
use ::relay::{Cluster, LoadBalancer};
use streamhub::StreamHubHandle;
use std::sync::Arc;

pub mod types;
pub mod hub;
pub mod balancer;
pub mod relay;
pub mod server;

use types::*;
pub use server::*;

/// Application state shared across all RPC handlers
#[derive(Clone)]
pub struct AppState {
    pub hub_handle: Option<StreamHubHandle>,
    pub load_balancer: Option<Arc<LoadBalancer>>,
    pub cluster: Option<Arc<Cluster>>,
    pub tls_fingerprint: Option<String>,
}

impl AppState {
    pub fn new(
        hub_handle: Option<StreamHubHandle>,
        load_balancer: Option<Arc<LoadBalancer>>,
        cluster: Option<Arc<Cluster>>,
        tls_fingerprint: Option<String>,
    ) -> Self {
        Self {
            hub_handle,
            load_balancer,
            cluster,
            tls_fingerprint,
        }
    }
}

/// Create the main RPC router with all route groups
pub fn create_router(state: AppState) -> Router {
    Router::new()
        // Hub management routes
        .nest("/hub", hub::create_routes())
        // Load balancer routes
        .nest("/balancer", balancer::create_routes())
        // Relay routes (for MoQ compatibility)
        .nest("/relay", relay::create_routes())
        // System routes
        .route("/health", axum::routing::get(health_check))
        .route("/version", axum::routing::get(version_info))
        .with_state(state)
}

/// Health check endpoint
async fn health_check(State(_state): State<AppState>) -> axum::Json<HealthResponse> {
    // Hub temporarily disabled
    let hub_status = false; // state.hub_handle.as_ref().map(|h| h.is_connected()).unwrap_or(false);
    
    axum::Json(HealthResponse {
        status: "ok".to_string(),
        hub_connected: hub_status,
        timestamp: chrono::Utc::now().timestamp(),
    })
}

/// Version information endpoint
async fn version_info() -> axum::Json<VersionResponse> {
    axum::Json(VersionResponse {
        name: "edify".to_string(),
        version: env!("CARGO_PKG_VERSION").to_string(),
        build_date: std::env::var("VERGEN_BUILD_DATE").unwrap_or_else(|_| "unknown".to_string()),
        git_hash: std::env::var("VERGEN_GIT_SHA").unwrap_or_else(|_| "unknown".to_string()),
    })
}