use axum::{Router, http::Method};
use relay::{<PERSON><PERSON>, LoadBalancer};
use streamhub::StreamHubHandle;
use tower_http::cors::{CorsLayer, Any};
use std::sync::Arc;

use super::{AppState, create_router};

/// Configuration for the RPC server
#[derive(Clone)]
pub struct RpcServerConfig {
    pub bind_address: std::net::SocketAddr,
    pub hub_handle: Option<StreamHubHandle>,
    pub load_balancer: Option<Arc<LoadBalancer>>,
    pub cluster: Option<Arc<Cluster>>,
}

/// RPC Server that handles all JSON-RPC requests
pub struct RpcServer {
    app: Router,
    bind_addr: std::net::SocketAddr,
}

impl RpcServer {
    /// Create a new RPC server with the given configuration
    pub fn new(config: RpcServerConfig) -> Self {
        // Create application state
        let state = AppState::new(
            config.hub_handle,
            config.load_balancer,
            config.cluster,
            None, // TODO: Add TLS fingerprint to config
        );

        // Create the main router with all route groups
        let app = create_router(state)
            // Add CORS middleware
            .layer(
                CorsLayer::new()
                    .allow_origin(Any)
                    .allow_methods([Method::GET, Method::POST, Method::DELETE, Method::PUT])
                    .allow_headers(Any),
            );

        Self { 
            app,
            bind_addr: config.bind_address,
        }
    }

    /// Run the RPC server
    pub async fn run(self) -> anyhow::Result<()> {
        tracing::info!("Starting RPC server on {}", self.bind_addr);
        
        let listener = tokio::net::TcpListener::bind(self.bind_addr).await?;
        axum::serve(listener, self.app).await?;
            
        Ok(())
    }

}

// =============================================================================
// CONFIGURATION BUILDER
// =============================================================================

/// Builder pattern for RPC server configuration
pub struct RpcServerConfigBuilder {
    bind_address: Option<std::net::SocketAddr>,
    hub_handle: Option<StreamHubHandle>,
    load_balancer: Option<Arc<LoadBalancer>>,
    cluster: Option<Arc<Cluster>>,
}

impl Default for RpcServerConfigBuilder {
    fn default() -> Self {
        Self::new()
    }
}

impl RpcServerConfigBuilder {
    pub fn new() -> Self {
        Self {
            bind_address: None,
            hub_handle: None,
            load_balancer: None,
            cluster: None,
        }
    }

    pub fn bind_address(mut self, addr: std::net::SocketAddr) -> Self {
        self.bind_address = Some(addr);
        self
    }

    pub fn hub_handle(mut self, handle: StreamHubHandle) -> Self {
        self.hub_handle = Some(handle);
        self
    }

    pub fn load_balancer(mut self, balancer: Arc<LoadBalancer>) -> Self {
        self.load_balancer = Some(balancer);
        self
    }

    pub fn cluster(mut self, cluster: Arc<Cluster>) -> Self {
        self.cluster = Some(cluster);
        self
    }

    pub fn build(self) -> Result<RpcServerConfig, &'static str> {
        let bind_address = self.bind_address
            .ok_or("bind_address is required")?;

        Ok(RpcServerConfig {
            bind_address,
            hub_handle: self.hub_handle,
            load_balancer: self.load_balancer,
            cluster: self.cluster,
        })
    }
}

// =============================================================================
// HELPER FUNCTIONS
// =============================================================================


/// Create a basic RPC server for testing/development
pub fn _create_dev_server(port: u16) -> anyhow::Result<RpcServer> {
    let bind_addr = format!("127.0.0.1:{}", port)
        .parse()
        .map_err(|e| anyhow::anyhow!("Invalid bind address: {}", e))?;

    let config = RpcServerConfigBuilder::new()
        .bind_address(bind_addr)
        .build()
        .map_err(|e| anyhow::anyhow!("Failed to build config: {}", e))?;

    Ok(RpcServer::new(config))
}

/// Create RPC server with all components
pub fn create_full_server(
    bind_addr: std::net::SocketAddr,
    hub_handle: Option<StreamHubHandle>,
    load_balancer: Option<Arc<LoadBalancer>>,
    cluster: Option<Arc<Cluster>>,
) -> anyhow::Result<RpcServer> {
    let mut builder = RpcServerConfigBuilder::new()
        .bind_address(bind_addr);

    if let Some(handle) = hub_handle {
        builder = builder.hub_handle(handle);
    }

    if let Some(balancer) = load_balancer {
        builder = builder.load_balancer(balancer);
    }

    if let Some(cluster) = cluster {
        builder = builder.cluster(cluster);
    }

    let config = builder
        .build()
        .map_err(|e| anyhow::anyhow!("Failed to build config: {}", e))?;

    Ok(RpcServer::new(config))
}