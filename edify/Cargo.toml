[package]
name = "edify"
version = "0.1.0"
edition = "2024"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
anyhow = { version = "1.0.98", features = ["backtrace"] }
moq-native = { version = "0.6" }
hang = "0.2.0"
moq-lite = {version = "0.2.0", features = ["serde"] }

url = "2"
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0.140"
uuid = { version = "1.17.0", features = ["serde", "v4"] }
chrono = { version = "0.4", features = ["serde"] }

tokio = { version = "1.44.2", features = ["full"] }
futures = "0.3"
once_cell = "1"
axum = { version = "0.8", features = ["tokio"] }
tower-http = { version = "0.6.2", features = ["cors"] }
http-body = "1.0.1"
hyper-serve = { version = "0.6", features = [
	"tls-rustls",
] } # fork of axum-serverrml_rtmp = "0.8.0"

tracing = "0.1.41"
tracing-subscriber = { version = "0.3.19", features = ["env-filter"] }
thiserror = "2.0.12"

bytes = "1"
indexmap = "2.9.0"
md5 = "0.7.0"
clap = {version = "4.5.37", features = ["env"] }
dotenvy = "0.15"
derive_builder = "0.20.2"

stream_client = { path = "../crates/stream_client" }
streamhub = { path = "../crates/streamhub" }
config = { path = "../crates/config" }
relay = { path = "../crates/relay" }

