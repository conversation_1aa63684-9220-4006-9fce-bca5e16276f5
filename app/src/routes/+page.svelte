<script lang="ts">
  import { onMount, onDestroy } from "svelte";
  import { MoQPublisher, checkMoQSupport } from "../lib/moq-publisher";
  import type { Device } from "../lib/moq-publisher";

  // Svelte 5 runes for state management
  let broadcastName = $state(`demo/user-${Math.floor(Math.random() * 10000)}`);
  let broadcastUrl = $state(`http://localhost:4443/`);
  let status = $state("Initializing...");
  let videoElement = $state<HTMLVideoElement>();
  let watchLink = $state(
    // svelte-ignore state_referenced_locally
        `subscribe-demo.html?name=${encodeURIComponent(broadcastName)}`
  );
  let publisher = $state<MoQPublisher>();
  let supportMessage = $state("");
  let isInitialized = $state(false);

  // Log initial state for debugging
  // svelte-ignore state_referenced_locally
    console.log("🔍 Initial state:", { broadcastName, broadcastUrl, status });

  // Publisher controls
  async function startCamera() {
    console.log("🎥 Starting camera...");
    try {
      if (!publisher) {
        status = "Error: Publisher not ready";
        return;
      }

      if (!isInitialized) {
        await initializePublisher();
      }

      await publisher.setDevice("camera");
      console.log("✅ Camera started successfully");
    } catch (error) {
      console.error("❌ Camera start error:", error);
      status = `Error: ${error instanceof Error ? error.message : "Unknown error"}`;
    }
  }

  async function startScreen() {
    console.log("🖥️ Starting screen share...");
    try {
      if (!publisher) {
        status = "Error: Publisher not ready";
        return;
      }

      if (!isInitialized) {
        await initializePublisher();
      }

      await publisher.setDevice("screen");
      console.log("✅ Screen share started successfully");
    } catch (error) {
      console.error("❌ Screen share error:", error);
      status = `Error: ${error instanceof Error ? error.message : "Unknown error"}`;
    }
  }

  async function stopPublishing() {
    console.log("🛑 Stopping publishing...");
    try {
      if (!publisher) {
        status = "Error: Publisher not ready";
        return;
      }

      await publisher.setDevice(null);
      console.log("✅ Publishing stopped successfully");
    } catch (error) {
      console.error("❌ Stop publishing error:", error);
      status = `Error: ${error instanceof Error ? error.message : "Unknown error"}`;
    }
  }

  // Initialize MoQ connection
  async function initializePublisher() {
    if (!publisher || isInitialized) return;

    try {
      await publisher.initialize();
      isInitialized = true;
      console.log("✅ Publisher initialized");
    } catch (error) {
      console.error("❌ Publisher initialization failed:", error);
      status = `Connection failed: ${error instanceof Error ? error.message : "Unknown error"}`;
      throw error;
    }
  }

  // Initialize the publisher when component mounts
  onMount(async () => {
    console.log("🚀 Component mounted, initializing...");

    try {
      // Check MoQ support
      const support = checkMoQSupport();
      supportMessage = support.message;

      if (!support.supported) {
        status = `Not supported: ${support.message}`;
        return;
      }

      // Create MoQ publisher
      publisher = new MoQPublisher({
        url: broadcastUrl,
        audio: true,
        video: true,
      });

      // Set up event listeners
      publisher.onStatusChange((connectionStatus) => {
        console.log("📊 Connection status changed:", connectionStatus);
        if (connectionStatus === "connected") {
          status = "Connected - Ready to publish";
        } else if (connectionStatus === "connecting") {
          status = "Connecting to MoQ relay...";
        } else {
          status = "Disconnected";
        }
      });

      publisher.onDeviceChange((device) => {
        console.log("📱 Device changed:", device);
        if (device) {
          status = `Publishing ${device}`;
        } else if (isInitialized) {
          status = "Connected - Ready to publish";
        }
      });

      console.log("✅ MoQ Publisher created successfully");
      status = "Ready to connect - Click a button to start";
    } catch (error) {
      console.error("❌ Setup error:", error);
      status = `Setup error: ${error instanceof Error ? error.message : "Unknown error"}`;
    }
  });

  // Attach video element when it becomes available
  $effect(() => {
    if (publisher && videoElement) {
      publisher.attachVideoElement(videoElement);
      console.log("📺 Video element attached to publisher");
    }
  });

  // Cleanup on component destroy
  onDestroy(async () => {
    if (publisher) {
      await publisher.close();
      console.log("🧹 Publisher cleaned up");
    }
  });

  // Reactive effect to update status display
  $effect(() => {
    console.log("📊 Status updated:", status);
  });
</script>

<svelte:head>
  <title>MoQ Publishing Demo</title>
  <meta name="viewport" content="width=device-width, initial-scale=1" />
</svelte:head>

<h1>MoQ Publishing Demo</h1>

<div class="nav">
  <a href="publish-demo.html">Publishing Demo</a> |
  <a href="subscribe-demo.html">Subscribing Demo</a> |
  <a href="conference-demo.html">Conferencing Demo</a>
</div>

<!-- Browser support info -->
<div class="container">
  <h2>Browser Support</h2>
  <p class="support-message">{supportMessage}</p>
</div>

<div class="container">
  <h2>Publish Your Media</h2>
  <p>
    This demo shows how to publish your camera or screen using the MoQ (Media
    over QUIC) protocol.
  </p>

  <!-- Video preview -->
  <video bind:this={videoElement} muted autoplay controls></video>

  <div class="controls">
    <button onclick={startCamera}>📹 Camera</button>
    <button onclick={startScreen}>🖥️ Screen Share</button>
    <button onclick={stopPublishing}>⏹️ Stop Publishing</button>
  </div>

  <div class="status">Status: {status}</div>
</div>

<div class="container">
  <h2>How to Use</h2>
  <ol>
    <li>
      Click <strong>📹 Camera</strong> to publish your webcam and microphone
    </li>
    <li>
      Click <strong>🖥️ Screen Share</strong> to publish your screen (with audio if
      supported)
    </li>
    <li>Click <strong>⏹️ Stop Publishing</strong> to stop the broadcast</li>
  </ol>
  <p>Your broadcast URL: <code>{broadcastUrl}</code></p>
  <p>
    Broadcast name: <code>{broadcastName}</code>
  </p>
  <p>
    You can view this broadcast in the <a href={watchLink}>Subscribe Demo</a>
  </p>
</div>

<div class="container">
  <h2>About MoQ</h2>
  <p>
    Media over QUIC (MoQ) is a live media delivery protocol utilizing QUIC. It
    uses modern web technologies like WebTransport and WebCodecs to provide
    efficient, low-latency streaming.
  </p>
  <p>
    This implementation uses the <code>@kixelated/moq</code> library and requires
    a MoQ relay server running on localhost:4443.
  </p>
</div>

<style>
  :global(body) {
    font-family:
      system-ui,
      -apple-system,
      BlinkMacSystemFont,
      "Segoe UI",
      Roboto,
      sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f5f5f5;
    color: #333;
  }

  h1,
  h2 {
    color: #2c3e50;
  }

  .container {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
  }

  .controls {
    display: flex;
    gap: 10px;
    margin: 15px 0;
    flex-wrap: wrap;
  }

  button {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s;
    font-size: 14px;
    font-weight: 500;
  }

  button:hover {
    background-color: #2980b9;
  }

  button:active {
    background-color: #1f6391;
  }

  .status {
    margin-top: 15px;
    padding: 12px;
    border-radius: 6px;
    background-color: #f8f9fa;
    border-left: 4px solid #6c757d;
    font-family: monospace;
    font-size: 14px;
  }

  .support-message {
    padding: 10px;
    border-radius: 4px;
    background-color: #e3f2fd;
    border-left: 4px solid #2196f3;
    margin: 10px 0;
    font-family: monospace;
    font-size: 14px;
  }

  code {
    background-color: #f8f9fa;
    padding: 3px 6px;
    border-radius: 4px;
    font-family: "Courier New", monospace;
    font-size: 13px;
    color: #e83e8c;
  }

  .nav {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
  }

  .nav a {
    color: #3498db;
    text-decoration: none;
    font-weight: 500;
  }

  .nav a:hover {
    text-decoration: underline;
  }

  video {
    width: 100%;
    max-height: 400px;
    border-radius: 8px;
    background-color: #000;
    margin-bottom: 15px;
  }

  ol {
    padding-left: 20px;
  }

  ol li {
    margin-bottom: 8px;
    line-height: 1.5;
  }

  p {
    line-height: 1.6;
    margin-bottom: 10px;
  }
</style>
